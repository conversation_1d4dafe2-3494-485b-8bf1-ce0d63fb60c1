/**
 * Deno Deploy主入口文件
 * 统一的服务器实现，自动适配本地和Deno Deploy环境
 */

import { NeteaseEAPI, type QualityLevel, type SongInfo } from "./netease-eapi.ts";

// 环境检测
const isDenoDeploy = !!Deno.env.get('DENO_DEPLOYMENT_ID');

// 配置管理
interface Config {
  NETEASE_COOKIE: string;
  PORT: number;
  DEBUG: boolean;
  SEARCH_LIMIT: number;
  DOWNLOAD_CONCURRENCY: number;
}

// 加载配置
function loadConfig(): Config {
  const config: Config = {
    NETEASE_COOKIE: Deno.env.get('NETEASE_COOKIE') ||
      'MUSIC_U=00ADBFC16B463B48ECD360EA8E4F17E08D3C84F11BB143279EAF360E6B271A7B454E26A57FC8E319CEF1E667B696337D2DA3E8EE638B26B1AD180920972F1D4FB3205352501003C7115132A9FC61E916056DAD41C72073C7401128AD0B7074CD165E934457B38E9DEBFCBC464AB38DD1858C006E634F1029C27CA7033D9B211884E0083D6943FBF1735F70D7BFF33B50B8C1216796ACC293ECF7B2ADF8661988B05D009C9DB0BFD465864FCD7511A6BA0F28ADB971B02DD9C9B73F7696B7106FB8C1D6E6F79BEF185B6522DA69C15C74FD8F0A854C1FA8AF34011ACE82F403AD80BC44E9485373633E2C0A6CCC51051C404CF2273D6036FFF4ABFBA7A5A919962C1C0D9FD1F95FDADEDE7B29CDDEB3D08EF28783D293F11531EAC72768CE5B38119E919BB12B422C4A7701319F24660C062E97D3FA222ABDC5837D09951548013567509A0CB73478B0DAA7F0A4299F17B8B93483B3F21A771C0506F266956BFF02726500FCB7197C99B30D7F7E3815DC61;os=pc;appver=8.9.75;',
    PORT: parseInt(Deno.env.get('PORT') || (isDenoDeploy ? '8000' : '3002')),
    DEBUG: (Deno.env.get('DEBUG') || 'false').toLowerCase() === 'true',
    SEARCH_LIMIT: parseInt(Deno.env.get('SEARCH_LIMIT') || '50'),
    DOWNLOAD_CONCURRENCY: parseInt(Deno.env.get('DOWNLOAD_CONCURRENCY') || '3')
  };

  // 验证Cookie
  if (!config.NETEASE_COOKIE.includes('MUSIC_U=')) {
    console.error('❌ 错误：未配置有效的NETEASE_COOKIE');
    if (isDenoDeploy) {
      console.log('🌐 请在Deno Deploy控制台设置环境变量 NETEASE_COOKIE');
    } else {
      console.log('💻 请设置环境变量：export NETEASE_COOKIE="your_cookie"');
    }
  }

  return config;
}

const config = loadConfig();
const api = new NeteaseEAPI();

// 主页HTML
const getHomePage = (): string => `
<!DOCTYPE html>
<html>
<head>
    <title>🦕 ${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deno Deploy' : 'Deno'} 网易云音乐解析器</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', 'Georgia', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background:
                radial-gradient(circle at 15% 85%, rgba(120, 119, 198, 0.2) 0%, transparent 60%),
                radial-gradient(circle at 85% 15%, rgba(255, 107, 157, 0.25) 0%, transparent 55%),
                radial-gradient(circle at 45% 45%, rgba(196, 113, 237, 0.15) 0%, transparent 65%),
                radial-gradient(circle at 70% 80%, rgba(18, 194, 233, 0.1) 0%, transparent 50%),
                linear-gradient(135deg,
                    #0a0a0f 0%,
                    #1a1a2e 20%,
                    #16213e 40%,
                    #0f3460 60%,
                    #1a1a2e 80%,
                    #0a0a0f 100%);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            animation: lonelyBackgroundFlow 30s ease-in-out infinite;
        }

        @keyframes lonelyBackgroundFlow {
            0%, 100% {
                background-position: 0% 0%, 100% 100%, 50% 50%, 30% 70%, 0% 0%;
                background-size: 120% 120%, 110% 110%, 130% 130%, 100% 100%, 100% 100%;
            }
            25% {
                background-position: 20% 30%, 80% 70%, 60% 40%, 50% 50%, 5% 5%;
                background-size: 130% 130%, 120% 120%, 140% 140%, 110% 110%, 105% 105%;
            }
            50% {
                background-position: 40% 60%, 60% 40%, 70% 30%, 70% 30%, 10% 10%;
                background-size: 140% 140%, 130% 130%, 150% 150%, 120% 120%, 110% 110%;
            }
            75% {
                background-position: 70% 20%, 30% 80%, 80% 20%, 20% 80%, 15% 15%;
                background-size: 130% 130%, 120% 120%, 140% 140%, 110% 110%, 105% 105%;
            }
        }

        /* 孤独美学装饰元素 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 30%),
                radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.01) 0%, transparent 25%);
            pointer-events: none;
            z-index: 1;
            animation: shadowDance 20s ease-in-out infinite;
        }

        @keyframes shadowDance {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }
        /* 孤独美学主容器 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
        }

        .container {
            background: rgba(26, 26, 46, 0.85);
            border: 2px solid rgba(255, 255, 255, 0.15);
            border-radius: 40px; /* 0.8倍缩小 */
            padding: 64px; /* 0.8倍缩小 */
            box-shadow:
                0 32px 80px rgba(0, 0, 0, 0.6), /* 0.8倍缩小 */
                0 0 160px rgba(255, 107, 157, 0.1), /* 0.8倍缩小 */
                inset 0 2px 0 rgba(255, 255, 255, 0.1),
                inset 0 -2px 0 rgba(255, 255, 255, 0.05);
            position: relative;
            overflow: hidden;
            width: 100%;
            max-width: 800px; /* 0.8倍缩小 */
            /* 移除浮动动画 */
        }

        /* 移除容器浮动动画 */

        /* 容器内部光影效果 */
        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 20%, rgba(255, 107, 157, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(196, 113, 237, 0.03) 0%, transparent 40%);
            pointer-events: none;
            border-radius: 40px; /* 0.8倍缩小 */
            animation: innerGlow 12s ease-in-out infinite;
        }

        @keyframes innerGlow {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.7; }
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
        }

        h1 {
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 20px;
            font-size: 3.2em;
            font-weight: 800;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(255, 107, 157, 0.8) 30%,
                rgba(196, 113, 237, 0.8) 70%,
                rgba(255, 255, 255, 0.9) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
            animation: titleGlow 6s ease-in-out infinite;
        }

        @keyframes titleGlow {
            0%, 100% {
                text-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
                transform: scale(1);
            }
            50% {
                text-shadow: 0 6px 30px rgba(196, 113, 237, 0.5);
                transform: scale(1.02);
            }
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 1.3em;
            margin-bottom: 30px;
            font-weight: 400;
            letter-spacing: 0.5px;
        }
        /* 流动诗意选项卡 */
        .tabs {
            display: flex;
            margin: 40px 0;
            background: rgba(22, 33, 62, 0.8);
            border-radius: 30px;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.12);
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .tabs::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg,
                rgba(255, 107, 157, 0.1) 0%,
                rgba(196, 113, 237, 0.1) 50%,
                rgba(18, 194, 233, 0.1) 100%);
            animation: tabGlow 8s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes tabGlow {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }

        .tab-button {
            flex: 1;
            background: none;
            border: none;
            padding: 20px 30px;
            cursor: pointer;
            font-size: 17px;
            font-weight: 700;
            color: rgba(255, 255, 255, 0.7);
            border-radius: 25px;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            z-index: 1;
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.3) 0%,
                rgba(196, 113, 237, 0.3) 50%,
                rgba(18, 194, 233, 0.3) 100%);
            opacity: 0;
            transition: all 0.4s ease;
            border-radius: 25px;
        }

        .tab-button::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
            transition: all 0.4s ease;
            transform: translate(-50%, -50%);
            border-radius: 50%;
        }

        .tab-button:hover::before,
        .tab-button.active::before {
            opacity: 1;
        }

        .tab-button:hover::after,
        .tab-button.active::after {
            width: 120px;
            height: 120px;
        }

        .tab-button:hover,
        .tab-button.active {
            color: rgba(255, 255, 255, 0.95);
            transform: translateY(-3px) scale(1.02);
            box-shadow:
                0 15px 40px rgba(255, 107, 157, 0.4),
                0 5px 20px rgba(196, 113, 237, 0.3);
        }
        .tab-content {
            display: none;
            animation: fadeIn 0.6s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 诗意表单区域 */
        .form-section {
            background: rgba(22, 33, 62, 0.6);
            padding: 40px;
            border-radius: 30px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
        }

        .form-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.05) 0%,
                rgba(196, 113, 237, 0.05) 100%);
            pointer-events: none;
        }

        .form-group {
            margin: 30px 0;
            position: relative;
        }

        label {
            display: block;
            margin-bottom: 12px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2em;
            letter-spacing: 0.5px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        input, select, textarea {
            width: 100%;
            padding: 20px 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            font-size: 17px;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(15, 52, 96, 0.7);
            color: rgba(255, 255, 255, 0.9);
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            font-family: inherit;
        }

        /* 特殊处理select下拉框 */
        select {
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 20px center;
            background-size: 20px;
            padding-right: 55px;
        }

        select option {
            background: rgba(26, 26, 46, 0.95);
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            border: none;
            font-weight: 500;
        }

        select option:checked {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.8) 0%,
                rgba(196, 113, 237, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
        }

        input::placeholder, textarea::placeholder {
            color: rgba(255, 255, 255, 0.5);
            font-weight: 400;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: rgba(255, 107, 157, 0.6);
            background: rgba(255, 255, 255, 0.12);
            box-shadow:
                0 0 0 4px rgba(255, 107, 157, 0.2),
                0 15px 35px rgba(255, 107, 157, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateY(-3px) scale(1.02);
        }
        /* 流动诗意按钮 */
        button {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.8) 0%,
                rgba(196, 113, 237, 0.8) 50%,
                rgba(18, 194, 233, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            padding: 22px 40px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 700;
            width: 100%;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 1.5px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow:
                0 15px 35px rgba(255, 107, 157, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.4) 50%,
                transparent 100%);
            transition: left 0.6s ease;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(255, 107, 157, 0.5),
                0 10px 30px rgba(196, 113, 237, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.9) 0%,
                rgba(196, 113, 237, 0.9) 50%,
                rgba(18, 194, 233, 0.9) 100%);
        }

        button:active {
            transform: translateY(-2px) scale(0.98);
            transition: all 0.1s ease;
        }
        /* 《偷影子的人》孤独美学文学引用 */
        .literary-quote {
            color: rgba(255, 255, 255, 0.95);
            font-size: 1.44em; /* 0.8倍缩小 */
            font-style: italic;
            margin: 48px 0; /* 0.8倍缩小 */
            padding: 40px 32px; /* 0.8倍缩小 */
            background: rgba(22, 33, 62, 0.7);
            border: 2px solid rgba(255, 107, 157, 0.2);
            border-radius: 32px; /* 0.8倍缩小 */
            position: relative;
            animation: lonelyFloat 12s ease-in-out infinite;
            text-align: center;
            font-family: 'Georgia', 'Crimson Text', 'Times New Roman', serif;
            line-height: 1.9;
            box-shadow:
                0 20px 48px rgba(0, 0, 0, 0.4), /* 0.8倍缩小 */
                0 0 80px rgba(255, 107, 157, 0.15), /* 0.8倍缩小 */
                inset 0 2px 0 rgba(255, 255, 255, 0.1);
            font-weight: 300;
            letter-spacing: 0.8px; /* 0.8倍缩小 */
        }

        @keyframes lonelyFloat {
            0%, 100% {
                transform: translateY(0px) scale(1) rotateX(0deg);
                box-shadow:
                    0 25px 60px rgba(0, 0, 0, 0.4),
                    0 0 100px rgba(255, 107, 157, 0.15),
                    inset 0 2px 0 rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 107, 157, 0.2);
            }
            33% {
                transform: translateY(-15px) scale(1.02) rotateX(1deg);
                box-shadow:
                    0 35px 80px rgba(0, 0, 0, 0.5),
                    0 0 120px rgba(196, 113, 237, 0.25),
                    inset 0 2px 0 rgba(255, 255, 255, 0.2);
                border-color: rgba(196, 113, 237, 0.3);
            }
            66% {
                transform: translateY(-8px) scale(1.01) rotateX(-0.5deg);
                box-shadow:
                    0 30px 70px rgba(0, 0, 0, 0.45),
                    0 0 110px rgba(18, 194, 233, 0.2),
                    inset 0 2px 0 rgba(255, 255, 255, 0.15);
                border-color: rgba(18, 194, 233, 0.25);
            }
        }

        .literary-quote::before {
            content: '"';
            position: absolute;
            left: 12px; /* 0.8倍缩小 */
            top: -12px; /* 0.8倍缩小 */
            font-size: 4.8em; /* 0.8倍缩小 */
            color: rgba(255, 107, 157, 0.3);
            font-family: 'Georgia', serif;
            line-height: 1;
            animation: quoteGlow 8s ease-in-out infinite;
        }

        .literary-quote::after {
            content: '"';
            position: absolute;
            right: 12px; /* 0.8倍缩小 */
            bottom: -28px; /* 0.8倍缩小 */
            font-size: 4.8em; /* 0.8倍缩小 */
            color: rgba(196, 113, 237, 0.3);
            font-family: 'Georgia', serif;
            line-height: 1;
            animation: quoteGlow 8s ease-in-out infinite 4s;
        }

        @keyframes quoteGlow {
            0%, 100% {
                opacity: 0.3;
                text-shadow: 0 0 16px rgba(255, 107, 157, 0.3); /* 0.8倍缩小 */
            }
            50% {
                opacity: 0.6;
                text-shadow: 0 0 32px rgba(196, 113, 237, 0.5); /* 0.8倍缩小 */
            }
        }

        .quote-author {
            display: block;
            margin-top: 30px;
            font-size: 1em;
            color: rgba(255, 255, 255, 0.8);
            font-style: normal;
            font-weight: 400;
            letter-spacing: 2px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: authorFade 6s ease-in-out infinite;
        }

        @keyframes authorFade {
            0%, 100% { opacity: 0.8; }
            50% { opacity: 1; }
        }

        /* 简单的底部状态文字 */
        .simple-status {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9em;
            text-align: center;
            pointer-events: none;
        }

        /* 诗意结果区域 */
        .result {
            margin-top: 40px;
            padding: 35px;
            border-radius: 30px;
            animation: resultSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            position: relative;
            overflow: hidden;
        }

        @keyframes resultSlideIn {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .success {
            background: linear-gradient(135deg,
                rgba(79, 172, 254, 0.8) 0%,
                rgba(0, 242, 254, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            box-shadow:
                0 20px 50px rgba(79, 172, 254, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .error {
            background: linear-gradient(135deg,
                rgba(250, 112, 154, 0.8) 0%,
                rgba(254, 225, 64, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            box-shadow:
                0 20px 50px rgba(250, 112, 154, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .loading {
            background: linear-gradient(135deg,
                rgba(168, 237, 234, 0.8) 0%,
                rgba(254, 214, 227, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            text-align: center;
            box-shadow:
                0 20px 50px rgba(168, 237, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }
        /* 诗意信息网格 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .info-item {
            background: rgba(22, 33, 62, 0.7);
            padding: 25px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.12);
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.15);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .info-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.03) 0%,
                rgba(196, 113, 237, 0.03) 100%);
            pointer-events: none;
        }

        .info-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.3),
                0 0 30px rgba(255, 107, 157, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 107, 157, 0.3);
        }

        .info-item > div:first-child {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 0.95em;
            letter-spacing: 0.5px;
        }

        .info-item > div:last-child {
            color: rgba(255, 255, 255, 0.95);
            font-weight: 500;
            font-size: 1.1em;
            line-height: 1.4;
        }

        /* 诗意下载链接 */
        .download-link {
            display: inline-block;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.8) 0%,
                rgba(196, 113, 237, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            padding: 18px 35px;
            border-radius: 25px;
            text-decoration: none;
            margin: 25px 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 1.1em;
            letter-spacing: 1px;
            text-transform: uppercase;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow:
                0 10px 30px rgba(255, 107, 157, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .download-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 100%);
            transition: left 0.6s ease;
        }

        .download-link:hover::before {
            left: 100%;
        }

        .download-link:hover {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.9) 0%,
                rgba(196, 113, 237, 0.9) 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow:
                0 20px 50px rgba(255, 107, 157, 0.5),
                0 10px 30px rgba(196, 113, 237, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
        }
        /* 简化搜索结果 - 无动画 */
        .search-results {
            max-height: 500px;
            overflow-y: auto;
            background: rgba(22, 33, 62, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            margin-top: 25px;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .search-results::-webkit-scrollbar {
            width: 8px;
        }

        .search-results::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .search-results::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.6) 0%,
                rgba(196, 113, 237, 0.6) 100%);
            border-radius: 10px;
        }

        .search-results::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.8) 0%,
                rgba(196, 113, 237, 0.8) 100%);
        }

        .search-item {
            padding: 20px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            transition: background-color 0.2s ease, transform 0.2s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            will-change: transform;
        }

        .search-item:hover {
            background: rgba(255, 255, 255, 0.06);
            transform: translateX(3px);
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .song-info {
            flex: 1;
            z-index: 1;
            position: relative;
        }

        .song-name {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 8px;
            font-size: 1.1em;
            line-height: 1.3;
        }

        .song-artist {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.95em;
            font-weight: 400;
        }

        .song-actions {
            display: flex;
            gap: 15px;
            align-items: center;
            z-index: 1;
            position: relative;
        }
        /* 诗意动作按钮 */
        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 100%);
            transition: left 0.5s ease;
        }

        .action-btn:hover::before {
            left: 100%;
        }

        .play-btn {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.8) 0%,
                rgba(196, 113, 237, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 20px rgba(255, 107, 157, 0.3);
        }

        .play-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(255, 107, 157, 0.5);
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.9) 0%,
                rgba(196, 113, 237, 0.9) 100%);
        }

        .download-btn {
            background: linear-gradient(135deg,
                rgba(79, 172, 254, 0.8) 0%,
                rgba(0, 242, 254, 0.8) 100%);
            color: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        }

        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(79, 172, 254, 0.5);
            background: linear-gradient(135deg,
                rgba(79, 172, 254, 0.9) 0%,
                rgba(0, 242, 254, 0.9) 100%);
        }

        /* 诗意音质选择器 */
        .quality-selector {
            margin-left: 15px;
            padding: 8px 15px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .quality-selector:focus {
            outline: none;
            border-color: rgba(255, 107, 157, 0.6);
            background: rgba(255, 255, 255, 0.12);
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.2);
        }

        .quality-selector option {
            background: rgba(26, 26, 46, 0.95);
            color: rgba(255, 255, 255, 0.9);
            padding: 8px;
        }
        .audio-player {
            width: 100%;
            margin-top: 10px;
            border-radius: 8px;
        }

        /* Apple Music风格播放器 */
        .music-player {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            color: white;
            text-align: center;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        .music-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        .album-art {
            width: 200px;
            height: 200px;
            border-radius: 15px;
            margin: 0 auto 20px;
            background: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .song-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .song-artist {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 25px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }
        .player-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: rgba(255,255,255,0.2);
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }
        .play-pause-btn {
            width: 70px;
            height: 70px;
            font-size: 30px;
            background: rgba(255,255,255,0.9);
            color: #667eea;
        }
        .progress-container {
            margin: 20px 0;
            position: relative;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
            overflow: hidden;
            cursor: pointer;
        }
        .progress-fill {
            height: 100%;
            background: white;
            border-radius: 3px;
            transition: width 0.1s ease;
            width: 0%;
        }
        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            margin-top: 8px;
            opacity: 0.8;
        }
        .volume-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
        }
        .volume-slider {
            width: 100px;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }
        .close-player {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .close-player:hover {
            background: rgba(255,255,255,0.3);
        }
        /* 诗意播放列表容器 */
        .playlist-container {
            max-height: 500px;
            overflow-y: auto;
            background: rgba(22, 33, 62, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            margin-top: 25px;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .playlist-container::-webkit-scrollbar {
            width: 8px;
        }

        .playlist-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .playlist-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.6) 0%,
                rgba(196, 113, 237, 0.6) 100%);
            border-radius: 10px;
        }

        .playlist-item {
            padding: 18px 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .playlist-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(255, 107, 157, 0.05) 0%,
                rgba(196, 113, 237, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .playlist-item:hover::before {
            opacity: 1;
        }

        .playlist-item:hover {
            background: rgba(255, 255, 255, 0.06);
            transform: translateX(5px);
        }

        .playlist-item:last-child {
            border-bottom: none;
        }

        .playlist-checkbox {
            width: 20px;
            height: 20px;
            accent-color: #ff6b9d;
            cursor: pointer;
            z-index: 1;
            position: relative;
        }

        .playlist-info {
            flex: 1;
            z-index: 1;
            position: relative;
        }

        .playlist-name {
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 5px;
            font-size: 1.05em;
        }

        .playlist-artist {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9em;
            font-weight: 400;
        }

        /* 诗意全选按钮 */
        .select-all-btn {
            margin: 20px 0;
            padding: 15px 25px;
            background: rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow:
                0 8px 20px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .select-all-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow:
                0 12px 30px rgba(0, 0, 0, 0.15),
                0 0 20px rgba(255, 107, 157, 0.2);
            border-color: rgba(255, 107, 157, 0.3);
        }

        .batch-input {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }
        @media (max-width: 768px) {
            .container { padding: 16px; } /* 0.8倍缩小 */
            h1 { font-size: 1.6em; } /* 0.8倍缩小 */
            .info-grid { grid-template-columns: 1fr; }
            .tabs { flex-direction: column; }
            .tab-button { width: 100%; }
        }

        /* 简单苹果风格播放器 */
        .simple-player {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .simple-player h3 {
            margin: 0 0 15px 0;
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2em;
            text-align: center;
        }

        .simple-player audio {
            width: 100%;
            margin: 10px 0;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
        }

        .simple-player p {
            margin: 10px 0 0 0;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9em;
        }

    </style>
</head>
<body>
    <div class="main-container">
        <div class="container">
            <div class="header">
                <h1>🎵 网易云音乐解析器</h1>

                <!-- 动态文学引用 -->
                <div class="literary-quote">
                    最难过的是看到你和我在一起，你却显得如此孤单
                    <span class="quote-author">——《偷影子的人》</span>
                </div>
            </div>
        
        <!-- 功能选项卡 -->
        <div class="tabs">
            <button class="tab-button active" onclick="showTab('search')">🔍 在线搜索</button>
            <button class="tab-button" onclick="showTab('batch')">📦 批量下载</button>
        </div>

        <!-- 在线搜索 -->
        <div id="search" class="tab-content active">
            <div class="form-section">
                <div class="form-group">
                    <label for="searchKeyword">🔍 搜索关键词</label>
                    <input type="text" id="searchKeyword" placeholder="输入歌曲名、歌手名或专辑名">
                </div>
                <div class="form-group">
                    <label for="searchType">🎯 搜索类型</label>
                    <select id="searchType">
                        <option value="songs">歌曲</option>
                        <option value="artists">歌手</option>
                    </select>
                </div>
                <button onclick="searchMusic()">🔍 开始搜索</button>
                <div id="searchResults" class="search-results" style="display: none;"></div>
            </div>
        </div>
        
        <!-- 批量下载 -->
        <div id="batch" class="tab-content">
            <div class="form-section">
                <div class="form-group">
                    <label for="batchType">📦 批量类型</label>
                    <select id="batchType" onchange="updateBatchInterface()">
                        <option value="album">专辑ID</option>
                        <option value="playlist">歌单ID</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="batchInput">📝 输入ID</label>
                    <input type="text" id="batchInput" placeholder="请输入专辑或歌单ID">
                </div>
                <button onclick="loadPlaylist()">📋 加载歌曲列表</button>

                <div id="playlistContainer" style="display: none;">
                    <div class="form-group">
                        <button class="select-all-btn" onclick="toggleSelectAll()">🔄 全选/取消全选</button>
                        <div class="form-group">
                            <label for="batchLevel">🎧 音质等级</label>
                            <select id="batchLevel">
                                <option value="standard">标准音质 (128kbps)</option>
                                <option value="exhigh">极高音质 (320kbps)</option>
                                <option value="lossless" selected>无损音质 (FLAC)</option>
                                <option value="hires">Hi-Res音质</option>
                            </select>
                        </div>
                    </div>
                    <div class="playlist-container" id="playlistItems"></div>
                    <button onclick="downloadSelected()" style="margin-top: 15px;">📦 下载选中歌曲</button>
                </div>
            </div>
        </div>

            <div id="result"></div>
        </div>
    </div>

    <script>
        // 全局函数定义
        window.showTab = function(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            document.getElementById('result').innerHTML = '';
        };



        // 搜索音乐 - 增强版
        window.searchMusic = async function() {
            const keyword = document.getElementById('searchKeyword').value;
            const searchType = document.getElementById('searchType').value;
            const resultDiv = document.getElementById('result');
            const searchResultsDiv = document.getElementById('searchResults');

            if (!keyword.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>❌ 请输入搜索关键词</h3></div>';
                searchResultsDiv.style.display = 'none';
                return;
            }

            // 显示加载状态，但不移除show类，避免闪屏
            resultDiv.innerHTML = '<div class="result loading"><h3>🔍 正在搜索...</h3></div>';

            try {
                console.log('🔍 开始搜索:', keyword, searchType);

                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ keyword, type: searchType })
                });

                console.log('🔍 搜索响应状态:', response.status);
                console.log('🔍 搜索响应头:', Object.fromEntries(response.headers.entries()));

                if (!response.ok) {
                    throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
                }

                const data = await response.json();
                console.log('🔍 搜索响应数据:', data);

                if (!data) {
                    throw new Error('搜索API返回空数据');
                }

                if (data.status !== 200) {
                    throw new Error(data.error || \`搜索API返回错误状态: \${data.status}\`);
                }

                console.log('🔍 搜索数据验证通过，开始处理结果');

                if (searchType === 'songs' && data.songs) {
                        resultDiv.innerHTML = \`<div class="result success"><h3>🎵 找到 \${data.total} 首歌曲</h3></div>\`;

                        let resultsHtml = '';
                        data.songs.forEach(song => {
                            // 兼容不同的艺术家字段名称
                            const artists = song.artists || song.ar || [];
                            const artistNames = Array.isArray(artists) ? artists.map(a => a.name).join('/') : '未知艺术家';

                            resultsHtml += \`
                                <div class="search-item">
                                    <div class="song-info">
                                        <div class="song-name">\${song.name}</div>
                                        <div class="song-artist">\${artistNames}</div>
                                    </div>
                                    <div class="song-actions">
                                        <button class="action-btn play-btn" onclick="playPreview('\${song.id}', '\${song.name.replace(/'/g, "\\'")}', '\${artistNames.replace(/'/g, "\\'")}')">
                                            🎵 试听
                                        </button>
                                        <select class="quality-selector" id="quality_\${song.id}" style="margin-right: 5px;">
                                            <option value="standard">标准 (128k)</option>
                                            <option value="exhigh">极高 (320k)</option>
                                            <option value="lossless" selected>无损 (FLAC)</option>
                                            <option value="hires">Hi-Res</option>
                                        </select>
                                        <button class="action-btn download-btn" onclick="directDownload('\${song.id}', '\${song.name.replace(/'/g, "\\'")}', '\${artistNames.replace(/'/g, "\\'")}')">
                                            ⬇️ 下载
                                        </button>
                                    </div>
                                </div>
                            \`;
                        });

                        // 直接更新内容，无动画
                        searchResultsDiv.innerHTML = resultsHtml;
                        searchResultsDiv.style.display = 'block';

                    } else if (searchType === 'artists' && data.artists) {
                        resultDiv.innerHTML = \`<div class="result success"><h3>👤 找到 \${data.total} 位歌手</h3></div>\`;

                        let resultsHtml = '';
                        data.artists.forEach(artist => {
                            resultsHtml += \`
                                <div class="search-item">
                                    <div class="song-info">
                                        <div class="song-name">\${artist.name}</div>
                                        <div class="song-artist">歌手 • \${artist.albumSize || 0} 张专辑</div>
                                    </div>
                                    <div class="song-actions">
                                        <button class="action-btn play-btn" onclick="viewArtistAlbums('\${artist.id}', '\${artist.name.replace(/'/g, "\\'")}')">
                                            📀 查看专辑
                                        </button>
                                    </div>
                                </div>
                            \`;
                        });

                        // 直接更新内容，无动画
                        searchResultsDiv.innerHTML = resultsHtml;
                        searchResultsDiv.style.display = 'block';
                    } else {
                        resultDiv.innerHTML = '<div class="result error"><h3>❌ 没有找到相关结果</h3></div>';
                        searchResultsDiv.style.display = 'none';
                    }
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>❌ 没有找到相关结果</h3></div>';
                    searchResultsDiv.style.display = 'none';
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 搜索请求失败</h3><p>\${error.message}</p></div>\`;
                searchResultsDiv.style.display = 'none';
            }
        };

        // 简单苹果风格试听功能
        window.playPreview = async function(songId, songName, artist) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result loading"><h3>🎵 正在获取试听链接...</h3></div>';

            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: 'standard' })
                });

                const data = await response.json();

                if (data.status === 200 && data.url) {
                    resultDiv.innerHTML = \`
                        <div class="simple-player">
                            <h3>🎵 \${songName} - \${artist}</h3>
                            <audio controls autoplay>
                                <source src="\${data.url}" type="audio/mpeg">
                                您的浏览器不支持音频播放。
                            </audio>
                            <p>🎧 正在播放试听...</p>
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>❌ 无法获取试听链接</h3></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 试听失败</h3><p>\${error.message}</p></div>\`;
            }
        };

        // 查看歌手专辑 - 修复闪屏问题
        window.viewArtistAlbums = async function(artistId, artistName) {
            const resultDiv = document.getElementById('result');
            const searchResultsDiv = document.getElementById('searchResults');

            // 显示加载状态，但不移除show类，避免闪屏
            resultDiv.innerHTML = '<div class="result loading"><h3>📀 正在获取专辑列表...</h3></div>';

            try {
                const response = await fetch('/api/artist-albums', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ artistId })
                });

                const data = await response.json();

                if (data.status === 200) {
                    resultDiv.innerHTML = \`<div class="result success"><h3>📀 \${artistName} 的专辑 (\${data.albums.length} 张)</h3></div>\`;

                    let resultsHtml = '';
                    data.albums.forEach(album => {
                        resultsHtml += \`
                            <div class="search-item">
                                <div class="song-info">
                                    <div class="song-name">\${album.name}</div>
                                    <div class="song-artist">专辑 • \${album.size || 0} 首歌曲 • ID: \${album.id}</div>
                                </div>
                                <div class="song-actions">
                                    <button class="action-btn play-btn" onclick="loadAlbumFromSearch('\${album.id}', '\${album.name.replace(/'/g, "\\'")}')">
                                        📋 查看歌曲
                                    </button>
                                </div>
                            </div>
                        \`;
                    });

                    // 直接更新内容，无动画
                    searchResultsDiv.innerHTML = resultsHtml;
                    searchResultsDiv.style.display = 'block';
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 获取专辑失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                    searchResultsDiv.style.display = 'none';
                }
            } catch (error) {
                console.error('🔍 搜索错误:', error);
                resultDiv.innerHTML = \`
                    <div class="result error">
                        <h3>❌ 搜索失败</h3>
                        <p>\${error.message}</p>
                        <p style="font-size: 0.9em; color: #666;">请检查网络连接或稍后重试</p>
                    </div>
                \`;
                searchResultsDiv.style.display = 'none';
            }
        };

        // 从搜索结果加载专辑
        window.loadAlbumFromSearch = function(albumId, albumName) {
            // 切换到批量下载选项卡
            document.querySelector('[data-tab="batch"]').click();

            // 设置专辑类型和ID
            document.getElementById('batchType').value = 'album';
            document.getElementById('batchInput').value = albumId;

            // 自动加载专辑
            window.loadPlaylist();
        };





        // 直接下载函数 - 真正的文件下载
        window.directDownload = async function(songId, songName, artist) {
            const qualitySelector = document.getElementById('quality_' + songId);
            const quality = qualitySelector ? qualitySelector.value : 'lossless';
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '<div class="result loading"><h3>🔄 正在获取下载链接...</h3></div>';

            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: quality })
                });

                const data = await response.json();

                if (data.status === 200 && data.url) {
                    // 获取文件扩展名
                    const extension = quality === 'lossless' || quality === 'hires' ? 'flac' : 'mp3';
                    const filename = \`\${artist} - \${songName}.\${extension}\`;

                    // 使用fetch下载文件并创建blob
                    const audioResponse = await fetch(data.url);
                    const blob = await audioResponse.blob();

                    // 创建下载链接
                    const downloadUrl = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = filename;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();

                    // 安全移除DOM元素
                    setTimeout(() => {
                        try {
                            if (link.parentNode) {
                                link.parentNode.removeChild(link);
                            }
                            window.URL.revokeObjectURL(downloadUrl);
                        } catch (error) {
                            console.log('清理下载链接时出现小错误，可忽略:', error.message);
                        }
                    }, 100);

                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>✅ 下载完成</h3>
                            <p>文件名：\${filename}</p>
                            <p>音质：\${data.level || quality}</p>
                            <p>大小：\${data.size || '未知'}</p>
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>❌ 无法获取下载链接</h3></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 下载失败</h3><p>\${error.message}</p></div>\`;
            }
        };

        // 初始化音频事件监听器（空函数，避免错误）
        function initializeAudioEvents() {
            // 简化版本，不需要复杂的音频事件
        }

        // 单曲下载
        async function downloadSingle(songId, songName, artist) {
            const quality = document.getElementById('downloadQuality').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = '<div class="result loading"><h3>🔄 正在获取下载链接...</h3></div>';

            try {
                const response = await fetch('/api/song', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ids: songId, level: quality })
                });

                const data = await response.json();

                if (data.status === 200 && data.url) {
                    // 创建下载链接
                    const filename = \`\${artist} - \${songName}.\${quality === 'lossless' || quality === 'hires' ? 'flac' : 'mp3'}\`;
                    const link = document.createElement('a');
                    link.href = data.url;
                    link.download = filename;
                    link.click();

                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>✅ 下载开始</h3>
                            <p>文件名：\${filename}</p>
                            <p>音质：\${data.level}</p>
                            <p>大小：\${data.size}</p>
                        </div>
                    \`;
                } else {
                    resultDiv.innerHTML = '<div class="result error"><h3>❌ 无法获取下载链接</h3></div>';
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 下载失败</h3><p>\${error.message}</p></div>\`;
            }
        };

        // 加载歌单/专辑列表
        window.loadPlaylist = async function() {
            const batchType = document.getElementById('batchType').value;
            const batchInput = document.getElementById('batchInput').value;
            const resultDiv = document.getElementById('result');
            const playlistContainer = document.getElementById('playlistContainer');
            const playlistItems = document.getElementById('playlistItems');

            if (!batchInput.trim()) {
                resultDiv.innerHTML = '<div class="result error"><h3>❌ 请输入ID</h3></div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result loading"><h3>� 正在加载歌曲列表...</h3></div>';

            try {
                const response = await fetch('/api/playlist-info', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: batchType, id: batchInput.trim() })
                });

                const data = await response.json();

                if (data.status === 200) {
                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>📋 \${data.name}</h3>
                            <p>共 \${data.songs.length} 首歌曲</p>
                        </div>
                    \`;

                    let itemsHtml = '';
                    data.songs.forEach((song, index) => {
                        itemsHtml += \`
                            <div class="playlist-item">
                                <input type="checkbox" class="playlist-checkbox" id="song_\${song.id}" checked>
                                <div class="playlist-info">
                                    <div class="playlist-name">\${song.name}</div>
                                    <div class="playlist-artist">\${song.artist}</div>
                                </div>
                            </div>
                        \`;
                    });

                    playlistItems.innerHTML = itemsHtml;
                    playlistContainer.style.display = 'block';

                    // 存储歌曲数据供下载使用
                    window.currentPlaylistSongs = data.songs;
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 加载失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 加载请求失败</h3><p>\${error.message}</p></div>\`;
            }
        };

        // 全选/取消全选
        window.toggleSelectAll = function() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);

            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
        };

        // 下载选中歌曲
        window.downloadSelected = async function() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox:checked');
            const selectedSongs = Array.from(checkboxes).map(cb => {
                const songId = cb.id.replace('song_', '');
                return window.currentPlaylistSongs.find(song => song.id.toString() === songId);
            }).filter(song => song);

            if (selectedSongs.length === 0) {
                document.getElementById('result').innerHTML = '<div class="result error"><h3>❌ 请选择要下载的歌曲</h3></div>';
                return;
            }

            const batchLevel = document.getElementById('batchLevel').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = \`<div class="result loading"><h3>📦 正在下载 \${selectedSongs.length} 首歌曲...</h3><p>请耐心等待</p></div>\`;

            try {
                const response = await fetch('/api/batch-download', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        songs: selectedSongs,
                        level: batchLevel
                    })
                });

                // 检查响应类型
                const contentType = response.headers.get('Content-Type');

                if (contentType && contentType.includes('application/zip')) {
                    // ZIP文件响应
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);

                    // 从响应头获取文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = '网易云音乐批量下载.zip';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename[^;=\\n]*=((['"]).*?\\2|[^;\\n]*)/);
                        if (filenameMatch) {
                            filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
                        }
                    }

                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();

                    // 清理
                    setTimeout(() => {
                        try {
                            if (link.parentNode) {
                                link.parentNode.removeChild(link);
                            }
                            window.URL.revokeObjectURL(url);
                        } catch (error) {
                            console.log('清理下载链接时出现小错误，可忽略:', error.message);
                        }
                    }, 100);

                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>📦 压缩包下载完成</h3>
                            <p>文件名：\${filename}</p>
                            <p>歌曲数量：\${selectedSongs.length} 首</p>
                            <p>文件大小：\${(blob.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                    \`;
                    return; // 提前返回，不执行后续JSON处理
                }

                const data = await response.json();

                if (data.status === 200) {
                    if (data.downloadType === 'zip') {
                        // 压缩包下载
                        const link = document.createElement('a');
                        link.href = data.zipUrl;
                        link.download = data.zipFilename;
                        link.click();

                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>📦 批量下载完成</h3>
                                <p>已打包为压缩文件：\${data.zipFilename}</p>
                                <p>成功：\${data.success} 首，失败：\${data.failed} 首</p>
                            </div>
                        \`;
                    } else {
                        // 单独下载
                        data.results.forEach(result => {
                            if (result.success) {
                                const link = document.createElement('a');
                                link.href = result.url;
                                link.download = \`\${result.artist} - \${result.name}.\${result.format}\`;
                                link.click();
                            }
                        });

                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>📦 批量下载完成</h3>
                                <p>成功：\${data.success} 首，失败：\${data.failed} 首</p>
                            </div>
                        \`;
                    }
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 批量下载失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 批量下载请求失败</h3><p>\${error.message}</p></div>\`;
            }
        };

        // 播放器控制函数
        let currentAudio = null;
        let isPlaying = false;

        function initializePlayer() {
            currentAudio = document.getElementById('audioPlayer');
            const volumeSlider = document.getElementById('volumeSlider');

            if (currentAudio && volumeSlider) {
                currentAudio.volume = volumeSlider.value / 100;
            }
        }

        function toggleOldPlayPause() {
            if (!currentAudio) return;

            const playPauseBtn = document.getElementById('playPauseBtn');

            if (isPlaying) {
                currentAudio.pause();
                playPauseBtn.textContent = '▶';
                isPlaying = false;
            } else {
                currentAudio.play();
                playPauseBtn.textContent = '⏸';
                isPlaying = true;
            }
        }

        function previousTrack() {
            // 重新开始播放当前歌曲
            if (currentAudio) {
                currentAudio.currentTime = 0;
            }
        }

        function nextTrack() {
            // 这里可以实现下一首歌曲的逻辑
            console.log('下一首歌曲功能待实现');
        }

        function seekToOld(event) {
            if (!currentAudio) return;

            const progressBar = event.currentTarget;
            const rect = progressBar.getBoundingClientRect();
            const percent = (event.clientX - rect.left) / rect.width;
            currentAudio.currentTime = percent * currentAudio.duration;
        }

        function setVolume(value) {
            if (currentAudio) {
                currentAudio.volume = value / 100;
            }
        }

        function updateDuration() {
            if (!currentAudio) return;

            const totalTime = document.getElementById('totalTime');
            if (totalTime) {
                totalTime.textContent = formatTime(currentAudio.duration);
            }
        }

        function updateProgress() {
            if (!currentAudio) return;

            const progressFill = document.getElementById('progressFill');
            const currentTime = document.getElementById('currentTime');

            if (progressFill && currentTime) {
                const percent = (currentAudio.currentTime / currentAudio.duration) * 100;
                progressFill.style.width = percent + '%';
                currentTime.textContent = formatTime(currentAudio.currentTime);
            }
        }

        function formatTimeOld(seconds) {
            if (isNaN(seconds)) return '0:00';

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return \`\${minutes}:\${remainingSeconds.toString().padStart(2, '0')}\`;
        }

        function closeOldPlayer() {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
                isPlaying = false;
            }

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '';
        }

        // 下载选中歌曲
        window.downloadSelected = async function() {
            const checkboxes = document.querySelectorAll('.playlist-checkbox:checked');
            const selectedSongs = Array.from(checkboxes).map(cb => {
                const songId = cb.id.replace('song_', '');
                return window.currentPlaylistSongs.find(song => song.id.toString() === songId);
            }).filter(song => song);

            if (selectedSongs.length === 0) {
                document.getElementById('result').innerHTML = '<div class="result error"><h3>❌ 请选择要下载的歌曲</h3></div>';
                return;
            }

            const batchLevel = document.getElementById('batchLevel').value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = \`<div class="result loading"><h3>📦 正在下载 \${selectedSongs.length} 首歌曲...</h3><p>请耐心等待</p></div>\`;

            try {
                const response = await fetch('/api/batch-download', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        songs: selectedSongs,
                        level: batchLevel
                    })
                });

                // 检查响应类型
                const contentType = response.headers.get('Content-Type');

                if (contentType && contentType.includes('application/zip')) {
                    // ZIP文件响应
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);

                    // 从响应头获取文件名
                    const contentDisposition = response.headers.get('Content-Disposition');
                    let filename = '网易云音乐批量下载.zip';
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename[^;=\\n]*=((['"]).*?\\2|[^;\\n]*)/);
                        if (filenameMatch) {
                            filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''));
                        }
                    }

                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    link.style.display = 'none';
                    document.body.appendChild(link);
                    link.click();

                    // 清理
                    setTimeout(() => {
                        try {
                            if (link.parentNode) {
                                link.parentNode.removeChild(link);
                            }
                            window.URL.revokeObjectURL(url);
                        } catch (error) {
                            console.log('清理下载链接时出现小错误，可忽略:', error.message);
                        }
                    }, 100);

                    resultDiv.innerHTML = \`
                        <div class="result success">
                            <h3>📦 压缩包下载完成</h3>
                            <p>文件名：\${filename}</p>
                            <p>歌曲数量：\${selectedSongs.length} 首</p>
                            <p>文件大小：\${(blob.size / 1024 / 1024).toFixed(2)} MB</p>
                        </div>
                    \`;
                    return; // 提前返回，不执行后续JSON处理
                }

                const data = await response.json();

                if (data.status === 200) {
                    if (data.downloadType === 'zip') {
                        // 压缩包下载
                        const link = document.createElement('a');
                        link.href = data.zipUrl;
                        link.download = data.zipFilename;
                        link.click();

                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>📦 批量下载完成</h3>
                                <p>已打包为压缩文件：\${data.zipFilename}</p>
                                <p>成功：\${data.success} 首，失败：\${data.failed} 首</p>
                            </div>
                        \`;
                    } else {
                        // 单独下载
                        data.results.forEach(result => {
                            if (result.success) {
                                const link = document.createElement('a');
                                link.href = result.url;
                                link.download = \`\${result.artist} - \${result.name}.\${result.format}\`;
                                link.click();
                            }
                        });

                        resultDiv.innerHTML = \`
                            <div class="result success">
                                <h3>📦 批量下载完成</h3>
                                <p>成功：\${data.success} 首，失败：\${data.failed} 首</p>
                            </div>
                        \`;
                    }
                } else {
                    resultDiv.innerHTML = \`<div class="result error"><h3>❌ 批量下载失败</h3><p>\${data.error || '未知错误'}</p></div>\`;
                }
            } catch (error) {
                resultDiv.innerHTML = \`<div class="result error"><h3>❌ 批量下载请求失败</h3><p>\${error.message}</p></div>\`;
            }
        }

        // 简单的会员状态检测
        window.checkCookieStatus = async function() {
            const statusDiv = document.getElementById('simpleStatus');

            statusDiv.textContent = '检测会员状态中...';

            try {
                // 添加超时控制
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                const response = await fetch('/api/cookie-check', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
                }

                const data = await response.json();
                console.log('🍪 Cookie检测响应:', data);

                if (data.status === 200) {
                    if (data.valid) {
                        if (data.vipExpired) {
                            statusDiv.textContent = \`会员已过期 (剩余\${data.vipDaysLeft}天)\`;
                        } else if (data.vipDaysLeft <= 3) {
                            statusDiv.textContent = \`会员即将过期 (剩余\${data.vipDaysLeft}天)\`;
                        } else if (data.vipDaysLeft <= 7) {
                            statusDiv.textContent = \`会员剩余\${data.vipDaysLeft}天\`;
                        } else {
                            statusDiv.textContent = \`会员剩余\${data.vipDaysLeft}天\`;
                        }
                    } else {
                        statusDiv.textContent = 'Cookie已失效，请更新';
                    }
                } else {
                    statusDiv.textContent = \`检测失败: \${data.error || '未知错误'}\`;
                }
            } catch (error) {
                console.error('🍪 Cookie检测错误:', error);
                if (error.name === 'AbortError') {
                    statusDiv.textContent = '检测超时，请刷新重试';
                } else {
                    statusDiv.textContent = \`网络错误: \${error.message}\`;
                }
            }
        }






        // 页面初始化



        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 回车键支持
            const searchKeywordInput = document.getElementById('searchKeyword');

            if (searchKeywordInput) {
                searchKeywordInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') window.searchMusic();
                });
            }

            // 页面加载时自动检测Cookie状态
            window.checkCookieStatus();

            console.log('🦕 ' + (typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deno Deploy' : 'Deno') + ' 网易云音乐解析器已加载');
        });
    </script>

    <!-- 简单状态文字 -->
    <div class="simple-status" id="simpleStatus">
        检测会员状态中...
    </div>


</body>
</html>
`;

// 请求处理函数
async function handler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const pathname = url.pathname;

  // CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    if (pathname === '/') {
      return new Response(getHomePage(), {
        headers: { 'Content-Type': 'text/html; charset=utf-8', ...corsHeaders }
      });

    } else if (pathname === '/api/song' && req.method === 'POST') {
      // 单曲解析
      const body = await req.json();
      const { ids, url: songUrl, level = 'standard' } = body;
      const songId = ids || songUrl;

      if (!songId) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供歌曲ID'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      console.log(`🎵 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 解析歌曲: ${songId}, 音质: ${level}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      // 测试Cookie有效性
      console.log('🍪 测试Cookie有效性...');
      const cookieValid = await api.testCookie(cookies);
      console.log('🍪 Cookie测试结果:', cookieValid ? '✅ 有效' : '❌ 无效');

      const result = await api.url_v1(songId, level as QualityLevel, cookies);

      if (!result?.data?.[0]) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲信息'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const songData = result.data[0];

      if (!songData.url) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲URL，可能是版权限制或需要会员权限'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      // 获取歌曲详情
      let songInfo: any = {};
      try {
        songInfo = await api.getSongDetail(songId);
      } catch (error) {
        console.warn('获取歌曲详情失败:', error.message);
      }

      const responseData: SongInfo = {
        status: 200,
        name: songInfo?.name || `歌曲ID: ${songId}`,
        ar_name: songInfo?.ar?.map((artist: any) => artist.name).join('/') || '网易云音乐',
        al_name: songInfo?.al?.name || '专辑信息',
        level: api.getQualityName(level as QualityLevel),
        size: api.formatFileSize(songData.size),
        url: songData.url.replace('http://', 'https://'),
        br: songData.br,
        pic: songInfo?.al?.picUrl || '',
        debug: {
          requestedLevel: level,
          actualBr: songData.br,
          isLossless: api.isLosslessQuality(songData.br),
          environment: isDenoDeploy ? 'Deno Deploy' : 'Local'
        }
      };

      if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
        responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
      } else if (level === 'lossless') {
        responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
      }

      return new Response(JSON.stringify(responseData), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });

    } else if (pathname === '/api/search' && req.method === 'POST') {
      // 搜索功能
      const body = await req.json();
      const { keyword, type = 'songs' } = body;

      if (!keyword) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供搜索关键词'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      console.log(`🔍 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 搜索: ${keyword}, 类型: ${type}`);

      // 解析Cookie
      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        let result;
        if (type === 'songs') {
          result = await api.searchSongs(keyword, config.SEARCH_LIMIT);
          return new Response(JSON.stringify({
            status: 200,
            type: 'songs',
            total: result.songCount,
            songs: result.songs
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        } else if (type === 'artists') {
          result = await api.searchArtists(keyword, config.SEARCH_LIMIT);
          return new Response(JSON.stringify({
            status: 200,
            type: 'artists',
            total: result.artistCount,
            artists: result.artists
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        } else {
          throw new Error('不支持的搜索类型');
        }
      } catch (error) {
        console.error(`🔍 搜索错误详情:`, error);
        return new Response(JSON.stringify({
          status: 500,
          error: `搜索失败: ${error.message}`,
          debug: isDenoDeploy ? undefined : error.stack
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/artist-albums' && req.method === 'POST') {
      // 获取歌手专辑
      const body = await req.json();
      const { artistId } = body;

      if (!artistId) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供歌手ID'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      console.log(`👤 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 获取歌手专辑: ${artistId}`);

      try {
        const result = await api.getArtistAlbums(artistId);
        return new Response(JSON.stringify({
          status: 200,
          total: result.hotAlbums?.length || 0,
          albums: result.hotAlbums || []
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        console.error('获取歌手专辑失败:', error);
        return new Response(JSON.stringify({
          status: 500,
          error: error.message
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/cookie-check' && req.method === 'POST') {
      // Cookie有效性和会员状态检测
      console.log(`🍪 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 检测Cookie和会员状态`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        const isValid = await api.testCookie(cookies);

        if (!isValid) {
          return new Response(JSON.stringify({
            status: 200,
            valid: false,
            error: 'Cookie已失效'
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // 获取用户信息和会员状态
        const userInfo = await api.getUserInfo(cookies);
        const vipInfo = userInfo?.profile?.vipType || 0;
        // 使用viptypeVersion作为VIP到期时间（毫秒时间戳）
        const vipExpireTime = userInfo?.profile?.viptypeVersion || 0;

        // 计算会员剩余天数
        const now = Date.now();
        const vipDaysLeft = vipExpireTime > now ? Math.ceil((vipExpireTime - now) / (1000 * 60 * 60 * 24)) : 0;
        const vipExpired = vipExpireTime <= now;

        // 调试信息
        console.log(`🔍 VIP状态调试:
          当前时间: ${now} (${new Date(now).toLocaleString('zh-CN')})
          VIP过期时间: ${vipExpireTime} (${new Date(vipExpireTime).toLocaleString('zh-CN')})
          是否过期: ${vipExpired}
          剩余天数: ${vipDaysLeft}`);

        return new Response(JSON.stringify({
          status: 200,
          valid: true,
          vipType: vipInfo,
          vipExpired: vipExpired,
          vipDaysLeft: vipDaysLeft,
          vipExpireTime: new Date(vipExpireTime).toLocaleDateString('zh-CN'),
          isVip: vipInfo > 0,
          recommendation: vipExpired ? '会员已过期，部分功能受限' : vipDaysLeft <= 3 ? '会员即将过期，建议续费' : '状态正常'
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        console.error('Cookie检测失败:', error);
        return new Response(JSON.stringify({
          status: 500,
          valid: false,
          error: error.message
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/lyrics' && req.method === 'POST') {
      // 歌词API
      const body = await req.json();
      const { id } = body;

      console.log(`🎤 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 获取歌词: ${id}`);

      if (!id) {
        return new Response(JSON.stringify({
          status: 400,
          error: '缺少歌曲ID'
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      try {
        const lyrics = await api.getLyrics(id);
        console.log(`🎤 歌词获取成功: ${id}, 长度: ${lyrics?.lrc?.lyric?.length || 0}`);

        return new Response(JSON.stringify({
          status: 200,
          lyrics: lyrics
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        console.error(`🎤 歌词获取失败: ${id}, 错误: ${error.message}`);
        return new Response(JSON.stringify({
          status: 500,
          error: error.message
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/playlist-info' && req.method === 'POST') {
      // 获取歌单/专辑信息
      const body = await req.json();
      const { type, id } = body;

      console.log(`📋 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 获取歌单信息: 类型=${type}, ID=${id}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        let result;

        if (type === 'album') {
          result = await api.getAlbumInfo(id, cookies);
        } else if (type === 'playlist') {
          result = await api.getPlaylistInfo(id, cookies);
        } else {
          throw new Error('无效的类型参数');
        }

        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `获取歌单信息失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/batch-download' && req.method === 'POST') {
      // 智能批量下载API - 自动选择最佳策略
      const body = await req.json();
      const { songs, level } = body;

      console.log(`📦 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 智能批量下载: ${songs.length} 首歌曲, 音质=${level}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        const result = await api.smartBatchDownload(songs, level as QualityLevel, cookies, isDenoDeploy);

        // 如果是Deno Deploy环境且返回的是Response对象（ZIP文件），直接返回
        if (isDenoDeploy && result instanceof Response) {
          console.log(`📦 [Deploy] 返回ZIP文件响应`);
          return result;
        }

        // 否则返回JSON响应
        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        console.error(`📦 批量下载错误详情:`, error);
        return new Response(JSON.stringify({
          status: 500,
          error: `批量下载失败: ${error.message}`,
          debug: isDenoDeploy ? undefined : error.stack
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname.startsWith('/downloads/')) {
      // 静态文件服务 - 提供ZIP下载
      const filename = pathname.replace('/downloads/', '');
      const filePath = `./public/downloads/${filename}`;

      try {
        const file = await Deno.open(filePath, { read: true });
        const stat = await file.stat();

        return new Response(file.readable, {
          headers: {
            'Content-Type': 'application/zip',
            'Content-Disposition': `attachment; filename="${filename}"`,
            'Content-Length': stat.size.toString(),
            ...corsHeaders
          }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          status: 404,
          error: '文件不存在'
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/batch' && req.method === 'POST') {
      // 批量下载
      const body = await req.json();
      const { type, level = 'standard', songIds, albumId, playlistId } = body;

      console.log(`📦 [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 批量下载: 类型=${type}, 音质=${level}`);

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        let result;

        if (type === 'songs' && songIds && Array.isArray(songIds)) {
          result = await api.batchDownload(songIds, level as QualityLevel, cookies, config.DOWNLOAD_CONCURRENCY);
        } else if (type === 'album' && albumId) {
          result = await api.downloadAlbum(albumId, level as QualityLevel, cookies, config.DOWNLOAD_CONCURRENCY);
        } else if (type === 'playlist' && playlistId) {
          result = await api.downloadPlaylist(playlistId, level as QualityLevel, cookies, config.DOWNLOAD_CONCURRENCY);
        } else {
          throw new Error('无效的批量下载参数');
        }

        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });

      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `批量下载失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else {
      return new Response('Not Found', {
        status: 404,
        headers: corsHeaders
      });
    }

  } catch (error) {
    console.error(`❌ [${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deploy' : 'Local'}] 服务器错误:`, error);
    return new Response(JSON.stringify({
      status: 500,
      error: `服务器错误: ${error.message}`
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 启动信息
console.log(`🦕 ${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deno Deploy' : 'Deno'} 网易云音乐解析服务启动`);
console.log(`🌐 环境: ${typeof isDenoDeploy !== 'undefined' && isDenoDeploy ? 'Deno Deploy' : '本地环境'}`);
console.log(`🔍 Cookie状态: ${config.NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 有效' : '❌ 无效'}`);
console.log(`🎵 支持功能: 在线搜索、批量下载、试听播放`);

// 根据环境启动服务
if (isDenoDeploy) {
  // Deno Deploy环境 - 导出在文件末尾
  console.log(`🌐 Deno Deploy模式 - 等待请求`);
} else {
  // 本地环境
  const server = Deno.serve({ port: config.PORT }, handler);
  console.log(`📡 本地服务地址: http://localhost:${config.PORT}`);
}

// Deno Deploy导出（必须在顶层）
export default { fetch: handler };
