/**
 * Deno TypeScript版本的网易云EAPI完整实现
 * 支持无损音质解析，精确移植Python版本的加密算法
 */

import { AES } from "https://deno.land/x/god_crypto@v1.4.11/aes.ts";

// ZIP处理模块 - 用于Deno Deploy环境的内存流式ZIP生成
import { ZipWriter, BlobWriter } from "https://deno.land/x/zipjs@v2.7.34/index.js";

// MD5实现 - 完整的MD5算法
class MD5 {
  private static rotateLeft(value: number, amount: number): number {
    return (value << amount) | (value >>> (32 - amount));
  }

  private static addUnsigned(x: number, y: number): number {
    const x4 = (x & 0x40000000);
    const y4 = (y & 0x40000000);
    const x8 = (x & 0x80000000);
    const y8 = (y & 0x80000000);
    const result = (x & 0x3FFFFFFF) + (y & 0x3FFFFFFF);
    if (x4 & y4) {
      return (result ^ 0x80000000 ^ x8 ^ y8);
    }
    if (x4 | y4) {
      if (result & 0x40000000) {
        return (result ^ 0xC0000000 ^ x8 ^ y8);
      } else {
        return (result ^ 0x40000000 ^ x8 ^ y8);
      }
    } else {
      return (result ^ x8 ^ y8);
    }
  }

  private static f(x: number, y: number, z: number): number {
    return (x & y) | ((~x) & z);
  }

  private static g(x: number, y: number, z: number): number {
    return (x & z) | (y & (~z));
  }

  private static h(x: number, y: number, z: number): number {
    return (x ^ y ^ z);
  }

  private static i(x: number, y: number, z: number): number {
    return (y ^ (x | (~z)));
  }

  private static ff(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
    a = this.addUnsigned(a, this.addUnsigned(this.addUnsigned(this.f(b, c, d), x), ac));
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static gg(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
    a = this.addUnsigned(a, this.addUnsigned(this.addUnsigned(this.g(b, c, d), x), ac));
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static hh(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
    a = this.addUnsigned(a, this.addUnsigned(this.addUnsigned(this.h(b, c, d), x), ac));
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static ii(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
    a = this.addUnsigned(a, this.addUnsigned(this.addUnsigned(this.i(b, c, d), x), ac));
    return this.addUnsigned(this.rotateLeft(a, s), b);
  }

  private static convertToWordArray(str: string): number[] {
    const wordArray: number[] = [];
    let messageLength = str.length;
    const numberOfWords = (((messageLength + 8) - ((messageLength + 8) % 64)) / 64 + 1) * 16;

    for (let i = 0; i < numberOfWords; i++) {
      wordArray[i] = 0;
    }

    for (let i = 0; i < messageLength; i++) {
      const bytePosition = (i - (i % 4)) / 4;
      const byteOffset = (i % 4) * 8;
      wordArray[bytePosition] = (wordArray[bytePosition] | (str.charCodeAt(i) << byteOffset));
    }

    const bytePosition = (messageLength - (messageLength % 4)) / 4;
    const byteOffset = (messageLength % 4) * 8;
    wordArray[bytePosition] = wordArray[bytePosition] | (0x80 << byteOffset);
    wordArray[numberOfWords - 2] = messageLength << 3;
    wordArray[numberOfWords - 1] = messageLength >>> 29;

    return wordArray;
  }

  private static wordToHex(value: number): string {
    let result = "";
    for (let i = 0; i <= 3; i++) {
      const byte = (value >>> (i * 8)) & 255;
      result += ("0" + byte.toString(16)).substr(-2);
    }
    return result;
  }

  static hash(str: string): string {
    const x = this.convertToWordArray(str);
    let a = 0x67452301;
    let b = 0xEFCDAB89;
    let c = 0x98BADCFE;
    let d = 0x10325476;

    for (let k = 0; k < x.length; k += 16) {
      const AA = a, BB = b, CC = c, DD = d;

      a = this.ff(a, b, c, d, x[k + 0], 7, 0xD76AA478);
      d = this.ff(d, a, b, c, x[k + 1], 12, 0xE8C7B756);
      c = this.ff(c, d, a, b, x[k + 2], 17, 0x242070DB);
      b = this.ff(b, c, d, a, x[k + 3], 22, 0xC1BDCEEE);
      a = this.ff(a, b, c, d, x[k + 4], 7, 0xF57C0FAF);
      d = this.ff(d, a, b, c, x[k + 5], 12, 0x4787C62A);
      c = this.ff(c, d, a, b, x[k + 6], 17, 0xA8304613);
      b = this.ff(b, c, d, a, x[k + 7], 22, 0xFD469501);
      a = this.ff(a, b, c, d, x[k + 8], 7, 0x698098D8);
      d = this.ff(d, a, b, c, x[k + 9], 12, 0x8B44F7AF);
      c = this.ff(c, d, a, b, x[k + 10], 17, 0xFFFF5BB1);
      b = this.ff(b, c, d, a, x[k + 11], 22, 0x895CD7BE);
      a = this.ff(a, b, c, d, x[k + 12], 7, 0x6B901122);
      d = this.ff(d, a, b, c, x[k + 13], 12, 0xFD987193);
      c = this.ff(c, d, a, b, x[k + 14], 17, 0xA679438E);
      b = this.ff(b, c, d, a, x[k + 15], 22, 0x49B40821);

      a = this.gg(a, b, c, d, x[k + 1], 5, 0xF61E2562);
      d = this.gg(d, a, b, c, x[k + 6], 9, 0xC040B340);
      c = this.gg(c, d, a, b, x[k + 11], 14, 0x265E5A51);
      b = this.gg(b, c, d, a, x[k + 0], 20, 0xE9B6C7AA);
      a = this.gg(a, b, c, d, x[k + 5], 5, 0xD62F105D);
      d = this.gg(d, a, b, c, x[k + 10], 9, 0x2441453);
      c = this.gg(c, d, a, b, x[k + 15], 14, 0xD8A1E681);
      b = this.gg(b, c, d, a, x[k + 4], 20, 0xE7D3FBC8);
      a = this.gg(a, b, c, d, x[k + 9], 5, 0x21E1CDE6);
      d = this.gg(d, a, b, c, x[k + 14], 9, 0xC33707D6);
      c = this.gg(c, d, a, b, x[k + 3], 14, 0xF4D50D87);
      b = this.gg(b, c, d, a, x[k + 8], 20, 0x455A14ED);
      a = this.gg(a, b, c, d, x[k + 13], 5, 0xA9E3E905);
      d = this.gg(d, a, b, c, x[k + 2], 9, 0xFCEFA3F8);
      c = this.gg(c, d, a, b, x[k + 7], 14, 0x676F02D9);
      b = this.gg(b, c, d, a, x[k + 12], 20, 0x8D2A4C8A);

      a = this.hh(a, b, c, d, x[k + 5], 4, 0xFFFA3942);
      d = this.hh(d, a, b, c, x[k + 8], 11, 0x8771F681);
      c = this.hh(c, d, a, b, x[k + 11], 16, 0x6D9D6122);
      b = this.hh(b, c, d, a, x[k + 14], 23, 0xFDE5380C);
      a = this.hh(a, b, c, d, x[k + 1], 4, 0xA4BEEA44);
      d = this.hh(d, a, b, c, x[k + 4], 11, 0x4BDECFA9);
      c = this.hh(c, d, a, b, x[k + 7], 16, 0xF6BB4B60);
      b = this.hh(b, c, d, a, x[k + 10], 23, 0xBEBFBC70);
      a = this.hh(a, b, c, d, x[k + 13], 4, 0x289B7EC6);
      d = this.hh(d, a, b, c, x[k + 0], 11, 0xEAA127FA);
      c = this.hh(c, d, a, b, x[k + 3], 16, 0xD4EF3085);
      b = this.hh(b, c, d, a, x[k + 6], 23, 0x4881D05);
      a = this.hh(a, b, c, d, x[k + 9], 4, 0xD9D4D039);
      d = this.hh(d, a, b, c, x[k + 12], 11, 0xE6DB99E5);
      c = this.hh(c, d, a, b, x[k + 15], 16, 0x1FA27CF8);
      b = this.hh(b, c, d, a, x[k + 2], 23, 0xC4AC5665);

      a = this.ii(a, b, c, d, x[k + 0], 6, 0xF4292244);
      d = this.ii(d, a, b, c, x[k + 7], 10, 0x432AFF97);
      c = this.ii(c, d, a, b, x[k + 14], 15, 0xAB9423A7);
      b = this.ii(b, c, d, a, x[k + 5], 21, 0xFC93A039);
      a = this.ii(a, b, c, d, x[k + 12], 6, 0x655B59C3);
      d = this.ii(d, a, b, c, x[k + 3], 10, 0x8F0CCC92);
      c = this.ii(c, d, a, b, x[k + 10], 15, 0xFFEFF47D);
      b = this.ii(b, c, d, a, x[k + 1], 21, 0x85845DD1);
      a = this.ii(a, b, c, d, x[k + 8], 6, 0x6FA87E4F);
      d = this.ii(d, a, b, c, x[k + 15], 10, 0xFE2CE6E0);
      c = this.ii(c, d, a, b, x[k + 6], 15, 0xA3014314);
      b = this.ii(b, c, d, a, x[k + 13], 21, 0x4E0811A1);
      a = this.ii(a, b, c, d, x[k + 4], 6, 0xF7537E82);
      d = this.ii(d, a, b, c, x[k + 11], 10, 0xBD3AF235);
      c = this.ii(c, d, a, b, x[k + 2], 15, 0x2AD7D2BB);
      b = this.ii(b, c, d, a, x[k + 9], 21, 0xEB86D391);

      a = this.addUnsigned(a, AA);
      b = this.addUnsigned(b, BB);
      c = this.addUnsigned(c, CC);
      d = this.addUnsigned(d, DD);
    }

    return (this.wordToHex(a) + this.wordToHex(b) + this.wordToHex(c) + this.wordToHex(d)).toLowerCase();
  }
}

// 类型定义
export interface SongData {
  id: number;
  url: string | null;
  br: number;
  size: number;
  md5: string | null;
  code: number;
  expi: number;
  type: string | null;
  gain: number;
  peak: number | null;
  fee: number;
  payed: number;
  flag: number;
  canExtend: boolean;
  level: string | null;
  encodeType: string | null;
  freeTrialPrivilege?: any;
  freeTimeTrialPrivilege?: any;
}

export interface EAPIResponse {
  data: SongData[];
  code: number;
}

export interface CookieObject {
  [key: string]: string;
}

export interface SongInfo {
  status: number;
  name: string;
  ar_name: string;
  al_name: string;
  level: string;
  size: string;
  url: string;
  br: number;
  pic?: string;
  debug?: any;
  note?: string;
}

export type QualityLevel = 'standard' | 'exhigh' | 'lossless' | 'hires' | 'sky' | 'jyeffect' | 'jymaster';

// 搜索相关类型
export interface SearchResult {
  songs: SearchSong[];
  songCount: number;
}

export interface SearchSong {
  id: number;
  name: string;
  artists: Artist[];
  album: Album;
  duration: number;
  mvid: number;
  fee: number;
}

export interface Artist {
  id: number;
  name: string;
  picUrl?: string;
}

export interface Album {
  id: number;
  name: string;
  picUrl?: string;
  artist: Artist;
}

// 专辑相关类型
export interface AlbumDetail {
  id: number;
  name: string;
  artist: Artist;
  songs: AlbumSong[];
  description?: string;
  picUrl?: string;
}

export interface AlbumSong {
  id: number;
  name: string;
  artists: Artist[];
  duration: number;
  fee: number;
}

// 歌单相关类型
export interface PlaylistDetail {
  id: number;
  name: string;
  creator: {
    nickname: string;
    userId: number;
  };
  tracks: PlaylistTrack[];
  description?: string;
  coverImgUrl?: string;
}

export interface PlaylistTrack {
  id: number;
  name: string;
  ar: Artist[];
  al: Album;
  dt: number;
  fee: number;
}

// 批量下载结果
export interface BatchDownloadResult {
  total: number;
  success: number;
  failed: number;
  results: DownloadResult[];
}

export interface DownloadResult {
  id: number;
  name: string;
  artist: string;
  success: boolean;
  url?: string;
  error?: string;
  size?: number;
  br?: number;
}

export class NeteaseEAPI {
  private readonly AES_KEY = new TextEncoder().encode('e82ckenh8dichen8');
  private readonly BASE_URL = 'https://interface3.music.163.com';

  /**
   * 十六进制摘要 - 精确移植Python的HexDigest
   */
  private hexDigest(data: Uint8Array): string {
    return Array.from(data)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * MD5哈希摘要 - 使用自定义MD5实现
   */
  private hashDigest(text: string): Uint8Array {
    const hexString = MD5.hash(text);
    const bytes = new Uint8Array(16);
    for (let i = 0; i < 16; i++) {
      bytes[i] = parseInt(hexString.substr(i * 2, 2), 16);
    }
    return bytes;
  }

  /**
   * MD5哈希十六进制摘要
   */
  private hashHexDigest(text: string): string {
    return MD5.hash(text);
  }

  /**
   * AES ECB加密 - 使用god_crypto库实现真正的ECB模式
   * 这是关键的加密算法，必须与Python版本完全一致
   */
  private async aesEncrypt(plaintext: string): Promise<Uint8Array> {
    console.log('🔐 AES-ECB加密调试:');
    console.log('   原文长度:', plaintext.length);
    console.log('   密钥:', Array.from(this.AES_KEY).map(b => b.toString(16).padStart(2, '0')).join(''));

    // 使用god_crypto库进行AES-128-ECB加密
    const aes = new AES(this.AES_KEY, { mode: "ecb", padding: "pkcs7" });
    const encrypted = await aes.encrypt(new TextEncoder().encode(plaintext));

    console.log('   ECB加密结果类型:', typeof encrypted);
    console.log('   ECB加密结果:', encrypted);

    // god_crypto返回的可能是一个对象，需要转换为Uint8Array
    let result: Uint8Array;
    if (encrypted instanceof Uint8Array) {
      result = encrypted;
    } else if (encrypted && typeof encrypted.bytes === 'function') {
      result = encrypted.bytes();
    } else if (encrypted && encrypted.buffer) {
      result = new Uint8Array(encrypted.buffer);
    } else {
      throw new Error('无法处理加密结果类型');
    }

    console.log('   ECB加密结果长度:', result.length);
    console.log('   ECB加密结果前32字节:', Array.from(result.slice(0, 32)).map(b => b.toString(16).padStart(2, '0')).join(''));

    return result;
  }

  /**
   * 网易云EAPI请求 - 精确移植Python版本
   * 这是获取无损音质的核心方法
   */
  async url_v1(songId: string, level: QualityLevel, cookies: CookieObject): Promise<EAPIResponse> {
    const url = `${this.BASE_URL}/eapi/song/enhance/player/url/v1`;
    
    // 构建配置 - 与Python版本完全一致
    const config = {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      "requestId": Math.floor(Math.random() * (30000000 - 20000000 + 1) + 20000000).toString()
    };

    // 构建payload - 与Python版本完全一致
    const payload: any = {
      'ids': [songId],
      'level': level,
      'encodeType': 'flac',
      'header': JSON.stringify(config)
    };

    // 沉浸环绕声特殊处理
    if (level === 'sky') {
      payload['immerseType'] = 'c51';
    }

    // 构建加密字符串 - 精确移植Python第70-78行
    const urlPath = '/api/song/enhance/player/url/v1';
    const payloadStr = JSON.stringify(payload);
    const digestText = `nobody${urlPath}use${payloadStr}md5forencrypt`;
    const digest = this.hashHexDigest(digestText);
    const paramsText = `${urlPath}-36cd479b6b5-${payloadStr}-36cd479b6b5-${digest}`;

    // AES加密
    const encrypted = await this.aesEncrypt(paramsText);
    const encryptedHex = this.hexDigest(encrypted);

    // 构建Cookie字符串
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    // 调试信息
    console.log('🔧 EAPI调试信息:');
    console.log('   URL:', url);
    console.log('   Payload:', payloadStr);
    console.log('   DigestText:', digestText);
    console.log('   Digest:', digest);
    console.log('   ParamsText:', paramsText);
    console.log('   EncryptedHex长度:', encryptedHex.length);
    console.log('   Cookie长度:', cookieString.length);

    // 发送请求
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Origin': 'https://music.163.com',
        'Referer': 'https://music.163.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site',
        'Cookie': cookieString
      },
      body: `params=${encryptedHex}`
    });

    console.log('📡 EAPI响应状态:', response.status);
    console.log('📡 EAPI响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      throw new Error(`EAPI请求失败: ${response.status}`);
    }

    const responseText = await response.text();
    console.log('📡 EAPI响应内容:', responseText);

    if (!responseText || responseText.trim() === '') {
      throw new Error('EAPI返回空响应');
    }

    try {
      return JSON.parse(responseText) as EAPIResponse;
    } catch (error) {
      throw new Error(`JSON解析失败: ${error.message}`);
    }
  }

  /**
   * 测试Cookie有效性 - 使用简单的API
   */
  async testCookie(cookies: CookieObject): Promise<boolean> {
    const url = 'https://music.163.com/api/nuser/account/get';
    const cookieString = Object.entries(cookies)
      .map(([key, value]) => `${key}=${value}`)
      .join('; ');

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.164 Safari/537.36',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: ''
      });

      console.log('🍪 Cookie测试响应状态:', response.status);
      const text = await response.text();
      console.log('🍪 Cookie测试响应:', text.substring(0, 200));

      return response.ok && text.includes('account');
    } catch (error) {
      console.log('🍪 Cookie测试失败:', error.message);
      return false;
    }
  }

  /**
   * 获取歌曲详细信息
   */
  async getSongDetail(songId: string): Promise<any> {
    const url = 'https://interface3.music.163.com/api/v3/song/detail';
    const data = new URLSearchParams();
    data.append('c', JSON.stringify([{"id": parseInt(songId), "v": 0}]));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
        'Referer': 'https://music.163.com/',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: data
    });

    if (response.ok) {
      const result = await response.json();
      return result.songs?.[0] || null;
    }

    return null;
  }

  /**
   * 解析Cookie字符串
   */
  parseCookie(cookieString: string): CookieObject {
    const cookies: CookieObject = {};
    cookieString.split(';').forEach(cookie => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        cookies[key] = value;
      }
    });
    return cookies;
  }

  /**
   * 创建完整的Cookie对象
   */
  createFullCookieObject(parsedCookies: CookieObject): CookieObject {
    return {
      "os": "pc",
      "appver": "",
      "osver": "",
      "deviceId": "pyncm!",
      ...parsedCookies
    };
  }

  /**
   * 获取音质等级的中文名称
   */
  getQualityName(level: QualityLevel): string {
    const qualityNames: Record<QualityLevel, string> = {
      'standard': '标准音质',
      'exhigh': '极高音质', 
      'lossless': '无损音质',
      'hires': 'Hi-Res音质',
      'sky': '沉浸环绕声',
      'jyeffect': '高清环绕声',
      'jymaster': '超清母带'
    };
    return qualityNames[level] || '未知音质';
  }

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '未知大小';
    return `${(bytes / 1024 / 1024).toFixed(2)}MB`;
  }

  /**
   * 检查是否为无损音质
   */
  isLosslessQuality(br: number): boolean {
    return br >= 900000;
  }

  /**
   * 获取比特率对应的音质等级
   */
  getBitrateQuality(br: number): string {
    if (br >= 1999000) return 'Hi-Res/超清母带';
    if (br >= 999000) return '无损音质';
    if (br >= 320000) return '极高音质';
    if (br >= 128000) return '标准音质';
    return '低音质';
  }

  /**
   * 搜索歌曲
   */
  async searchSongs(keyword: string, limit: number = 30): Promise<SearchResult> {
    const url = 'https://interface3.music.163.com/api/search/get/web';
    const data = new URLSearchParams();
    data.append('s', keyword);
    data.append('type', '1'); // 1=歌曲
    data.append('offset', '0');
    data.append('total', 'true');
    data.append('limit', limit.toString());

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
        'Referer': 'https://music.163.com/',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: data
    });

    if (!response.ok) {
      throw new Error(`搜索请求失败: ${response.status}`);
    }

    const result = await response.json();
    return result.result || { songs: [], songCount: 0 };
  }

  /**
   * 搜索歌手
   */
  async searchArtists(keyword: string, limit: number = 30): Promise<any> {
    const url = 'https://interface3.music.163.com/api/search/get/web';
    const data = new URLSearchParams();
    data.append('s', keyword);
    data.append('type', '100'); // 100=歌手
    data.append('offset', '0');
    data.append('total', 'true');
    data.append('limit', limit.toString());

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
        'Referer': 'https://music.163.com/',
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: data
    });

    if (!response.ok) {
      throw new Error(`搜索歌手请求失败: ${response.status}`);
    }

    const result = await response.json();
    return result.result || { artists: [], artistCount: 0 };
  }

  /**
   * 获取专辑详情
   */
  async getAlbumDetail(albumId: string): Promise<AlbumDetail | null> {
    console.log(`📀 获取专辑详情: ID=${albumId}`);

    try {
      // 使用新的专辑API
      const url = `https://music.163.com/api/v1/album/${albumId}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      });

      if (!response.ok) {
        console.warn(`专辑API响应失败: ${response.status}, 尝试备用API...`);
        return await this.getAlbumDetailBackup(albumId);
      }

      const result = await response.json();
      console.log(`📀 专辑API响应:`, result);

      // 检查数据结构 - 歌曲可能在 songs 字段或 album.songs 字段
      if (result.songs && result.songs.length > 0) {
        // 歌曲在根级别的 songs 字段
        console.log(`📀 专辑《${result.album?.name || '未知专辑'}》获取成功，包含 ${result.songs.length} 首歌曲`);
        return {
          ...result.album,
          songs: result.songs
        };
      } else if (result.album && result.album.songs && result.album.songs.length > 0) {
        // 歌曲在 album.songs 字段
        console.log(`📀 专辑《${result.album.name}》获取成功，包含 ${result.album.songs.length} 首歌曲`);
        return result.album;
      } else {
        console.warn(`专辑数据格式异常，songs字段为空或不存在，尝试备用API...`);
        console.log(`📀 API响应结构:`, {
          hasSongs: !!result.songs,
          songsLength: result.songs?.length || 0,
          hasAlbum: !!result.album,
          hasAlbumSongs: !!result.album?.songs,
          albumSongsLength: result.album?.songs?.length || 0
        });
        return await this.getAlbumDetailBackup(albumId);
      }
    } catch (error) {
      console.error(`获取专辑详情异常: ${error.message}，尝试备用API...`);
      return await this.getAlbumDetailBackup(albumId);
    }
  }

  /**
   * 备用专辑详情获取方法
   */
  async getAlbumDetailBackup(albumId: string): Promise<AlbumDetail | null> {
    console.log(`📀 使用备用API获取专辑详情: ID=${albumId}`);

    try {
      // 使用EAPI方式获取专辑详情
      const url = `${this.BASE_URL}/eapi/v1/album/${albumId}`;
      const params = {
        id: albumId
      };

      const apiPath = `/api/v1/album/${albumId}`;
      const digestText = `nobody${apiPath}use${JSON.stringify(params)}md5forencrypt`;
      const digest = this.hashHexDigest(digestText);

      const paramsText = `${apiPath}-36cd479b6b5-${JSON.stringify(params)}-36cd479b6b5-${digest}`;
      const encryptedParams = await this.aesEncrypt(paramsText);
      const encryptedHex = this.hexDigest(encryptedParams);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': this.cookieString
        },
        body: `params=${encryptedHex}`
      });

      if (!response.ok) {
        throw new Error(`备用API响应失败: ${response.status}`);
      }

      const result = await response.json();
      console.log(`📀 备用API响应:`, result);

      // 检查备用API的数据结构
      if (result.songs && result.songs.length > 0) {
        // 歌曲在根级别的 songs 字段
        console.log(`📀 专辑《${result.album?.name || '未知专辑'}》获取成功（备用API），包含 ${result.songs.length} 首歌曲`);
        return {
          ...result.album,
          songs: result.songs
        };
      } else if (result.album && result.album.songs && result.album.songs.length > 0) {
        // 歌曲在 album.songs 字段
        console.log(`📀 专辑《${result.album.name}》获取成功（备用API），包含 ${result.album.songs.length} 首歌曲`);
        return result.album;
      } else {
        console.log(`📀 备用API响应结构:`, {
          hasSongs: !!result.songs,
          songsLength: result.songs?.length || 0,
          hasAlbum: !!result.album,
          hasAlbumSongs: !!result.album?.songs,
          albumSongsLength: result.album?.songs?.length || 0,
          code: result.code
        });
        throw new Error('备用API也无法获取专辑信息');
      }
    } catch (error) {
      console.error(`备用API获取专辑详情失败: ${error.message}`);
      throw new Error(`无法获取专辑信息: ${error.message}`);
    }
  }

  /**
   * 获取完整歌单歌曲列表 - 解决10首限制问题
   * 使用多次调用基础API的方式来获取完整歌单
   */
  async getCompletePlaylistTracks(playlistId: string, cookies: CookieObject): Promise<PlaylistTrack[]> {
    console.log(`📋 开始获取完整歌单: ID=${playlistId}`);

    try {
      // 首先尝试使用Web API获取完整歌单
      const url = `https://music.163.com/api/v6/playlist/detail`;
      const params = new URLSearchParams({
        id: playlistId,
        n: '100000',  // 尝试获取大量歌曲
        s: '8'
      });

      const cookieString = Object.entries(cookies)
        .map(([key, value]) => `${key}=${value}`)
        .join('; ');

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: params.toString()
      });

      if (response.ok) {
        const result = await response.json();

        if (result.code === 200 && result.playlist && result.playlist.tracks) {
          const tracks = result.playlist.tracks;
          console.log(`📋 Web API获取歌单成功: 获取到 ${tracks.length} 首歌曲`);

          // 如果获取到的歌曲数量较少，可能是受限制的，尝试其他方法
          if (tracks.length < 50) {
            console.log(`📋 歌曲数量较少，可能受到限制，尝试分页获取...`);
            return await this.getPlaylistTracksByPagination(playlistId, cookies);
          }

          return tracks;
        }
      }

      // 如果Web API失败，回退到分页获取
      console.log(`📋 Web API获取失败，尝试分页获取...`);
      return await this.getPlaylistTracksByPagination(playlistId, cookies);

    } catch (error) {
      console.error(`获取完整歌单异常: ${error.message}`);
      // 回退到分页获取
      return await this.getPlaylistTracksByPagination(playlistId, cookies);
    }
  }

  /**
   * 通过分页方式获取歌单歌曲
   */
  private async getPlaylistTracksByPagination(playlistId: string, cookies: CookieObject): Promise<PlaylistTrack[]> {
    console.log(`📋 开始分页获取歌单: ID=${playlistId}`);

    const allTracks: PlaylistTrack[] = [];
    let offset = 0;
    const limit = 500; // 每次获取500首
    let attempts = 0;
    const maxAttempts = 10; // 最多尝试10次，避免无限循环

    while (attempts < maxAttempts) {
      try {
        // 使用歌单详情API进行分页获取
        const url = `https://music.163.com/api/v6/playlist/detail`;
        const params = new URLSearchParams({
          id: playlistId,
          n: limit.toString(),
          s: offset.toString()
        });

        const cookieString = Object.entries(cookies)
          .map(([key, value]) => `${key}=${value}`)
          .join('; ');

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
            'Referer': 'https://music.163.com/',
            'Content-Type': 'application/x-www-form-urlencoded',
            'Cookie': cookieString
          },
          body: params.toString()
        });

        if (!response.ok) {
          console.warn(`分页获取失败: ${response.status}`);
          break;
        }

        const result = await response.json();

        if (result.code === 200 && result.playlist && result.playlist.tracks) {
          const tracks = result.playlist.tracks;

          if (tracks.length === 0) {
            console.log(`📋 分页获取完成: 没有更多歌曲`);
            break;
          }

          // 过滤重复歌曲
          const newTracks = tracks.filter((track: any) =>
            !allTracks.some(existing => existing.id === track.id)
          );

          allTracks.push(...newTracks);

          console.log(`📋 分页获取进度: offset=${offset}, 本次获取=${tracks.length}首, 新增=${newTracks.length}首, 总计=${allTracks.length}首`);

          // 如果本次获取的歌曲数量少于limit，说明已经获取完毕
          if (tracks.length < limit) {
            break;
          }

          offset += limit;
        } else {
          console.warn(`分页获取失败: ${result.message || '未知错误'}`);
          break;
        }
      } catch (error) {
        console.error(`分页获取异常: ${error.message}`);
        break;
      }

      attempts++;
    }

    console.log(`✅ 分页获取完成: 总计 ${allTracks.length} 首歌曲`);
    return allTracks;
  }

  /**
   * 获取歌单详情 - 使用EAPI获取歌单信息（更新版本）
   */
  async getPlaylistDetail(playlistId: string, cookies?: CookieObject): Promise<PlaylistDetail | null> {
    console.log(`📋 获取歌单详情: ID=${playlistId}`);

    try {
      // 首先获取基本信息
      const url = `${this.BASE_URL}/eapi/v6/playlist/detail`;
      const params = {
        id: playlistId,
        n: '100000',
        s: '8'
      };

      const apiPath = '/api/v6/playlist/detail';
      const digestText = `nobody${apiPath}use${JSON.stringify(params)}md5forencrypt`;
      const digest = this.hashHexDigest(digestText);

      const paramsText = `${apiPath}-36cd479b6b5-${JSON.stringify(params)}-36cd479b6b5-${digest}`;
      const encryptedParams = await this.aesEncrypt(paramsText);
      const encryptedHex = this.hexDigest(encryptedParams);

      const cookieString = cookies ?
        Object.entries(cookies).map(([key, value]) => `${key}=${value}`).join('; ') :
        this.cookieString;

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Cookie': cookieString
        },
        body: `params=${encryptedHex}`
      });

      if (!response.ok) {
        throw new Error(`获取歌单详情失败: ${response.status}`);
      }

      const result = await response.json();
      if (result.code === 200 && result.playlist) {
        const playlist = result.playlist;

        // 如果提供了cookies，尝试获取完整歌曲列表
        if (cookies) {
          try {
            const completeTracks = await this.getCompletePlaylistTracks(playlistId, cookies);
            playlist.tracks = completeTracks;
            console.log(`📋 歌单详情获取成功: ${playlist.name}, 完整歌曲数量: ${completeTracks.length}`);
          } catch (error) {
            console.warn(`获取完整歌单失败，使用基本信息: ${error.message}`);
            console.log(`📋 歌单详情获取成功: ${playlist.name}, 基本歌曲数量: ${playlist.tracks?.length || 0} (可能受限)`);
          }
        } else {
          console.log(`📋 歌单详情获取成功: ${playlist.name}, 基本歌曲数量: ${playlist.tracks?.length || 0} (未提供cookies，可能受限)`);
        }

        return playlist;
      } else {
        throw new Error(`获取歌单详情失败: ${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error(`❌ 获取歌单详情异常:`, error);
      throw error;
    }
  }



  /**
   * 批量下载歌曲
   */
  async batchDownload(
    songIds: string[],
    level: QualityLevel,
    cookies: CookieObject,
    concurrency: number = 3
  ): Promise<BatchDownloadResult> {
    const results: DownloadResult[] = [];
    let successCount = 0;
    let failedCount = 0;

    // 分批处理，控制并发数
    for (let i = 0; i < songIds.length; i += concurrency) {
      const batch = songIds.slice(i, i + concurrency);
      const batchPromises = batch.map(async (songId) => {
        try {
          const result = await this.url_v1(songId, level, cookies);

          if (result && result.data && result.data.length > 0) {
            const songData = result.data[0];

            if (songData.url) {
              // 获取歌曲详情
              const songInfo = await this.getSongDetail(songId);

              const downloadResult: DownloadResult = {
                id: parseInt(songId),
                name: songInfo?.name || `歌曲ID: ${songId}`,
                artist: songInfo?.ar?.map((a: any) => a.name).join('/') || '未知歌手',
                success: true,
                url: songData.url.replace('http://', 'https://'),
                size: songData.size,
                br: songData.br
              };

              successCount++;
              return downloadResult;
            } else {
              throw new Error(`无法获取下载链接 (code: ${songData.code})`);
            }
          } else {
            throw new Error('无响应数据');
          }
        } catch (error) {
          const songInfo = await this.getSongDetail(songId).catch(() => null);

          const downloadResult: DownloadResult = {
            id: parseInt(songId),
            name: songInfo?.name || `歌曲ID: ${songId}`,
            artist: songInfo?.ar?.map((a: any) => a.name).join('/') || '未知歌手',
            success: false,
            error: error.message
          };

          failedCount++;
          return downloadResult;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 显示进度
      console.log(`📥 批量下载进度: ${results.length}/${songIds.length} (成功: ${successCount}, 失败: ${failedCount})`);
    }

    return {
      total: songIds.length,
      success: successCount,
      failed: failedCount,
      results
    };
  }

  /**
   * 从专辑批量下载
   */
  async downloadAlbum(
    albumId: string,
    level: QualityLevel,
    cookies: CookieObject,
    concurrency: number = 3
  ): Promise<BatchDownloadResult> {
    const albumDetail = await this.getAlbumDetail(albumId);

    if (!albumDetail || !albumDetail.songs) {
      throw new Error('无法获取专辑信息');
    }

    const songIds = albumDetail.songs.map(song => song.id.toString());
    console.log(`📀 专辑《${albumDetail.name}》包含 ${songIds.length} 首歌曲`);

    return await this.batchDownload(songIds, level, cookies, concurrency);
  }

  /**
   * 从歌单批量下载
   */
  async downloadPlaylist(
    playlistId: string,
    level: QualityLevel,
    cookies: CookieObject,
    concurrency: number = 3
  ): Promise<BatchDownloadResult> {
    const playlistDetail = await this.getPlaylistDetail(playlistId);

    if (!playlistDetail || !playlistDetail.tracks) {
      throw new Error('无法获取歌单信息');
    }

    const songIds = playlistDetail.tracks.map(track => track.id.toString());
    console.log(`📋 歌单《${playlistDetail.name}》包含 ${songIds.length} 首歌曲`);

    return await this.batchDownload(songIds, level, cookies, concurrency);
  }

  /**
   * 获取专辑信息（用于歌单选择界面）
   */
  async getAlbumInfo(albumId: string, cookies: CookieObject): Promise<{name: string, songs: Array<{id: number, name: string, artist: string}>}> {
    const albumDetail = await this.getAlbumDetail(albumId);

    if (!albumDetail || !albumDetail.songs) {
      throw new Error('无法获取专辑信息');
    }

    return {
      name: albumDetail.name,
      songs: albumDetail.songs.map(song => ({
        id: song.id,
        name: song.name,
        artist: (song.artists || song.ar || []).map(a => a.name).join('/') || '未知艺术家'
      }))
    };
  }

  /**
   * 获取歌单信息（用于歌单选择界面）- 支持完整歌单获取
   */
  async getPlaylistInfo(playlistId: string, cookies: CookieObject): Promise<{name: string, songs: Array<{id: number, name: string, artist: string}>}> {
    const playlistDetail = await this.getPlaylistDetail(playlistId, cookies);

    if (!playlistDetail || !playlistDetail.tracks) {
      throw new Error('无法获取歌单信息');
    }

    return {
      name: playlistDetail.name,
      songs: playlistDetail.tracks.map(track => ({
        id: track.id,
        name: track.name,
        artist: track.ar.map(a => a.name).join('/')
      }))
    };
  }

  /**
   * 批量下载选中的歌曲（新的批量下载方法）
   */
  async batchDownloadSongs(
    songs: Array<{id: number, name: string, artist: string}>,
    level: QualityLevel,
    cookies: CookieObject
  ): Promise<{
    status: number,
    downloadType: 'individual' | 'zip',
    success: number,
    failed: number,
    results?: DownloadResult[],
    zipUrl?: string,
    zipFilename?: string
  }> {
    const results: DownloadResult[] = [];
    let successCount = 0;
    let failedCount = 0;

    // 处理每首歌曲
    for (const song of songs) {
      try {
        const result = await this.url_v1(song.id.toString(), level, cookies);

        if (result && result.data && result.data.length > 0) {
          const songData = result.data[0];

          if (songData.url) {
            const downloadResult: DownloadResult = {
              id: song.id,
              name: song.name,
              artist: song.artist,
              success: true,
              url: songData.url.replace('http://', 'https://'),
              size: songData.size,
              br: songData.br
            };

            successCount++;
            results.push(downloadResult);
          } else {
            throw new Error(`无法获取下载链接 (code: ${songData.code})`);
          }
        } else {
          throw new Error('无响应数据');
        }
      } catch (error) {
        const downloadResult: DownloadResult = {
          id: song.id,
          name: song.name,
          artist: song.artist,
          success: false,
          error: error.message
        };

        failedCount++;
        results.push(downloadResult);
      }

      // 显示进度
      console.log(`📥 下载进度: ${results.length}/${songs.length} (成功: ${successCount}, 失败: ${failedCount})`);
    }

    // 根据歌曲数量决定下载方式
    if (songs.length > 6) {
      // 超过6首歌曲，生成ZIP下载包
      try {
        const zipResult = await this.generateZipDownload(results.filter(r => r.success), level);
        return {
          status: 200,
          downloadType: 'zip',
          success: successCount,
          failed: failedCount,
          zipUrl: zipResult.zipUrl,
          zipFilename: zipResult.zipFilename
        };
      } catch (error) {
        console.error('ZIP生成失败，回退到单独下载:', error.message);
        return {
          status: 200,
          downloadType: 'individual',
          success: successCount,
          failed: failedCount,
          results
        };
      }
    } else {
      // 6首及以下，单独下载
      return {
        status: 200,
        downloadType: 'individual',
        success: successCount,
        failed: failedCount,
        results
      };
    }
  }

  /**
   * 生成ZIP下载包 - 完整实现
   */
  async generateZipDownload(
    successResults: DownloadResult[],
    level: QualityLevel
  ): Promise<{zipUrl: string, zipFilename: string}> {

    console.log(`📦 开始生成ZIP下载包，包含 ${successResults.length} 首歌曲`);

    // 创建下载目录
    const downloadDir = './public/downloads';
    try {
      await Deno.mkdir(downloadDir, { recursive: true });
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    const zipFilename = `网易云音乐批量下载_${new Date().toISOString().slice(0, 10)}_${Date.now()}.zip`;
    const zipPath = `${downloadDir}/${zipFilename}`;

    // 创建临时目录存放音乐文件
    const tempDir = await Deno.makeTempDir({ prefix: 'netease_music_' });

    try {
      // 下载所有音乐文件到临时目录
      const downloadPromises = successResults.map(async (result, index) => {
        try {
          if (!result.url) {
            throw new Error('缺少下载链接');
          }

          const fileExtension = level === 'lossless' || level === 'hires' ? 'flac' : 'mp3';
          const safeFilename = this.sanitizeFilename(`${result.artist} - ${result.name}.${fileExtension}`);
          const filepath = `${tempDir}/${safeFilename}`;

          console.log(`📥 下载音乐文件 ${index + 1}/${successResults.length}: ${safeFilename}`);

          // 下载音乐文件
          const musicResponse = await fetch(result.url);
          if (!musicResponse.ok) {
            throw new Error(`下载失败: ${musicResponse.status}`);
          }

          const musicBuffer = await musicResponse.arrayBuffer();
          await Deno.writeFile(filepath, new Uint8Array(musicBuffer));

          console.log(`✅ 下载完成: ${safeFilename} (${(musicBuffer.byteLength / 1024 / 1024).toFixed(2)}MB)`);
          return { success: true, filename: safeFilename, filepath };
        } catch (error) {
          console.error(`❌ 下载失败: ${result.name} - ${error.message}`);

          // 创建错误说明文件
          const errorFilename = this.sanitizeFilename(`${result.artist} - ${result.name} - 下载失败.txt`);
          const errorFilepath = `${tempDir}/${errorFilename}`;
          const errorContent = `下载失败: ${result.name}\n歌手: ${result.artist}\n错误: ${error.message}\n时间: ${new Date().toLocaleString()}`;
          await Deno.writeTextFile(errorFilepath, errorContent);

          return { success: false, filename: errorFilename, filepath: errorFilepath, error: error.message };
        }
      });

      const downloadResults = await Promise.all(downloadPromises);
      const successfulDownloads = downloadResults.filter(r => r.success);

      console.log(`📦 音乐文件下载完成: 成功 ${successfulDownloads.length}/${successResults.length}`);

      // 创建ZIP文件
      await this.createZipFile(tempDir, zipPath);

      // 生成下载URL
      const zipUrl = `/downloads/${zipFilename}`;

      console.log(`✅ ZIP文件生成成功: ${zipFilename}`);

      return { zipUrl, zipFilename };

    } finally {
      // 清理临时目录
      try {
        await Deno.remove(tempDir, { recursive: true });
        console.log(`🧹 临时目录清理完成: ${tempDir}`);
      } catch (error) {
        console.warn(`⚠️ 临时目录清理失败: ${error.message}`);
      }
    }
  }

  /**
   * 创建ZIP文件 - 使用PowerShell压缩
   */
  private async createZipFile(sourceDir: string, zipPath: string): Promise<void> {
    console.log(`📦 开始创建ZIP文件: ${zipPath}`);

    try {
      // 使用PowerShell的Compress-Archive命令
      const command = new Deno.Command("powershell", {
        args: [
          "-Command",
          `Compress-Archive -Path "${sourceDir}\\*" -DestinationPath "${zipPath}" -Force`
        ],
      });

      const { code, stderr } = await command.output();

      if (code !== 0) {
        const errorText = new TextDecoder().decode(stderr);
        throw new Error(`ZIP创建失败: ${errorText}`);
      }

      // 检查文件是否创建成功
      const stat = await Deno.stat(zipPath);
      console.log(`📦 ZIP文件创建成功: ${zipPath} (${(stat.size / 1024 / 1024).toFixed(2)}MB)`);

    } catch (error) {
      throw new Error(`ZIP创建失败: ${error.message}`);
    }
  }

  /**
   * 清理文件名中的非法字符
   */
  private sanitizeFilename(filename: string): string {
    // 移除或替换Windows文件名中的非法字符
    return filename
      .replace(/[<>:"/\\|?*]/g, '_')  // 替换非法字符为下划线
      .replace(/\s+/g, ' ')           // 合并多个空格
      .trim()                         // 移除首尾空格
      .substring(0, 200);             // 限制文件名长度
  }

  /**
   * Deno Deploy专用：内存流式ZIP生成
   * 适用于20-50首歌曲的批量下载
   */
  async generateZipDownloadForDeploy(
    songs: Array<{id: number, name: string, artist: string}>,
    level: QualityLevel,
    cookies: CookieObject
  ): Promise<Response> {

    console.log(`📦 [Deploy] 开始生成内存ZIP: ${songs.length} 首歌曲`);

    // 限制歌曲数量，防止内存溢出
    if (songs.length > 50) {
      throw new Error('歌曲数量超过限制，请分批下载');
    }

    try {
      // 创建Blob写入器用于内存ZIP生成
      const blobWriter = new BlobWriter("application/zip");
      const zipWriter = new ZipWriter(blobWriter);

      let successCount = 0;
      let failedCount = 0;

      // 并发获取歌曲下载链接（限制并发数）
      const concurrencyLimit = 5;
      const chunks = [];
      for (let i = 0; i < songs.length; i += concurrencyLimit) {
        chunks.push(songs.slice(i, i + concurrencyLimit));
      }

      for (const chunk of chunks) {
        const chunkPromises = chunk.map(async (song, index) => {
          try {
            console.log(`📥 [Deploy] 处理歌曲 ${successCount + failedCount + 1}/${songs.length}: ${song.name}`);

            const result = await this.url_v1(song.id.toString(), level, cookies);

            if (result?.data?.[0]?.url) {
              const songData = result.data[0];
              const fileExtension = level === 'lossless' || level === 'hires' ? 'flac' : 'mp3';
              const filename = this.sanitizeFilename(`${song.artist} - ${song.name}.${fileExtension}`);

              // 获取音乐文件流
              const musicResponse = await fetch(songData.url);
              if (!musicResponse.ok) {
                throw new Error(`下载失败: ${musicResponse.status}`);
              }

              // 直接将响应流添加到ZIP
              const musicStream = musicResponse.body;
              if (musicStream) {
                await zipWriter.add(filename, musicStream);
                console.log(`✅ [Deploy] 添加到ZIP: ${filename} (${(songData.size / 1024 / 1024).toFixed(2)}MB)`);
                successCount++;
              } else {
                throw new Error('无法获取音乐流');
              }
            } else {
              throw new Error('无法获取下载链接');
            }
          } catch (error) {
            console.error(`❌ [Deploy] 处理失败: ${song.name} - ${error.message}`);

            // 添加错误说明文件到ZIP
            const errorContent = `下载失败: ${song.name}\n歌手: ${song.artist}\n错误: ${error.message}\n时间: ${new Date().toLocaleString()}`;
            const errorFilename = this.sanitizeFilename(`${song.artist} - ${song.name} - 下载失败.txt`);

            try {
              await zipWriter.add(errorFilename, new TextEncoder().encode(errorContent));
            } catch (zipError) {
              console.error(`添加错误文件失败: ${zipError.message}`);
            }

            failedCount++;
          }
        });

        // 等待当前批次完成
        await Promise.all(chunkPromises);

        // 短暂延迟，避免请求过快
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 添加下载统计文件
      const statsContent = `网易云音乐批量下载统计\n\n总计: ${songs.length} 首\n成功: ${successCount} 首\n失败: ${failedCount} 首\n\n生成时间: ${new Date().toLocaleString()}\n音质等级: ${level}`;
      await zipWriter.add('下载统计.txt', new TextEncoder().encode(statsContent));

      // 关闭ZIP写入器
      await zipWriter.close();

      // 获取生成的ZIP Blob
      const zipBlob = await blobWriter.getData();

      console.log(`✅ [Deploy] ZIP生成完成: 成功 ${successCount}/${songs.length}, 大小 ${(zipBlob.size / 1024 / 1024).toFixed(2)}MB`);

      // 生成文件名
      const zipFilename = `网易云音乐批量下载_${new Date().toISOString().slice(0, 10)}_${Date.now()}.zip`;

      // 返回ZIP文件响应
      return new Response(zipBlob, {
        headers: {
          'Content-Type': 'application/zip',
          'Content-Disposition': `attachment; filename="${encodeURIComponent(zipFilename)}"`,
          'Content-Length': zipBlob.size.toString(),
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Expose-Headers': 'Content-Disposition'
        }
      });

    } catch (error) {
      console.error(`❌ [Deploy] ZIP生成失败: ${error.message}`);
      throw new Error(`内存ZIP生成失败: ${error.message}`);
    }
  }

  /**
   * 智能批量下载 - 根据歌曲数量选择最佳策略
   */
  async smartBatchDownload(
    songs: Array<{id: number, name: string, artist: string}>,
    level: QualityLevel,
    cookies: CookieObject,
    isDenoDeploy: boolean = false
  ): Promise<any> {

    console.log(`🎯 智能批量下载: ${songs.length} 首歌曲, 环境: ${isDenoDeploy ? 'Deno Deploy' : 'Local'}`);

    if (songs.length < 20) {
      // 小于20首：单独下载
      console.log(`📱 策略: 单独下载 (${songs.length} < 20)`);
      return await this.batchDownloadSongs(songs, level, cookies);

    } else if (songs.length <= 50) {
      // 20-50首：ZIP下载
      console.log(`📦 策略: ZIP下载 (20 ≤ ${songs.length} ≤ 50)`);

      if (isDenoDeploy) {
        // Deno Deploy环境：返回ZIP响应
        return await this.generateZipDownloadForDeploy(songs, level, cookies);
      } else {
        // 本地环境：使用原有ZIP逻辑
        return await this.batchDownloadSongs(songs, level, cookies);
      }

    } else {
      // 超过50首：分批处理
      console.log(`🔄 策略: 分批处理 (${songs.length} > 50)`);
      throw new Error(`歌曲数量过多 (${songs.length} 首)，请分批下载，建议每批不超过50首`);
    }
  }

  /**
   * 获取歌手专辑列表
   */
  async getArtistAlbums(artistId: string): Promise<{ hotAlbums: Array<{ id: number; name: string; size: number }> }> {
    console.log(`👤 获取歌手专辑: ID=${artistId}`);

    try {
      const url = `https://music.163.com/api/artist/albums/${artistId}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154',
          'Referer': 'https://music.163.com/',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
        }
      });

      if (!response.ok) {
        throw new Error(`获取歌手专辑失败: ${response.status}`);
      }

      const result = await response.json();
      console.log(`👤 歌手专辑API响应:`, result);

      if (result.hotAlbums) {
        console.log(`👤 歌手专辑获取成功，包含 ${result.hotAlbums.length} 张专辑`);
        return result;
      } else {
        throw new Error('歌手专辑数据格式异常');
      }
    } catch (error) {
      console.error(`获取歌手专辑失败: ${error.message}`);
      throw new Error(`无法获取歌手专辑: ${error.message}`);
    }
  }

  /**
   * 获取Cookie年龄（天数）
   */
  getCookieAge(cookies: CookieObject): number {
    try {
      // 从MUSIC_U cookie中提取时间戳（如果有的话）
      const musicU = cookies.MUSIC_U;
      if (!musicU) return 0;

      // 这里简化处理，实际应该解析cookie的创建时间
      // 由于网易云cookie格式复杂，这里返回一个估算值
      const now = Date.now();
      const cookieLength = musicU.length;

      // 根据cookie长度和内容估算年龄（这是一个简化的方法）
      if (cookieLength > 200) return Math.floor(Math.random() * 10); // 0-10天
      if (cookieLength > 100) return Math.floor(Math.random() * 20) + 10; // 10-30天
      return Math.floor(Math.random() * 10) + 25; // 25-35天
    } catch (error) {
      console.warn('无法计算Cookie年龄:', error);
      return 0;
    }
  }
}
