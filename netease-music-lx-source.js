/**
 * @name 网易云音乐源
 * @description 基于网易云音乐API的高质量音源，支持无损FLAC和Hi-Res音频
 * @version 1.0.0
 * <AUTHOR> 4.0 sonnet
 * @homepage https://github.com/lyswhut/lx-music-desktop
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

// 配置你部署的服务器地址
const BASE_URL = 'https://your-domain.com' // 请替换为你的实际部署地址

// 音质映射
const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'exhigh', 
    'flac': 'lossless',
    'flac24bit': 'hires'
  }
}

// HTTP请求封装
const httpRequest = (url, options = {}) => new Promise((resolve, reject) => {
  const requestOptions = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    },
    timeout: 10000,
    ...options
  }
  
  request(url, requestOptions, (err, resp) => {
    if (err) return reject(err)
    try {
      const data = JSON.parse(resp.body)
      resolve(data)
    } catch (e) {
      reject(new Error('响应解析失败'))
    }
  })
})

// API接口
const apis = {
  wy: {
    // 获取音乐播放链接
    async musicUrl(musicInfo, quality) {
      try {
        const response = await httpRequest(`${BASE_URL}/api/song`, {
          body: JSON.stringify({
            ids: musicInfo.songmid || musicInfo.id,
            level: quality
          })
        })

        if (response.status === 200 && response.url) {
          return response.url
        } else {
          throw new Error(response.error || '获取播放链接失败')
        }
      } catch (error) {
        console.error('获取音乐URL失败:', error)
        throw error
      }
    },

    // 搜索音乐（如果落雪音乐支持自定义搜索）
    async search(keyword, page = 1) {
      try {
        const response = await httpRequest(`${BASE_URL}/api/search`, {
          body: JSON.stringify({
            keyword: keyword,
            type: 'songs'
          })
        })

        if (response.status === 200 && response.songs) {
          return response.songs.map(song => ({
            id: song.id,
            songmid: song.id,
            name: song.name,
            singer: song.artists?.map(a => a.name).join('/') || '未知艺术家',
            album: song.album?.name || '',
            albumId: song.album?.id,
            img: song.album?.picUrl,
            duration: Math.floor(song.duration / 1000),
            source: 'wy'
          }))
        } else {
          return []
        }
      } catch (error) {
        console.error('搜索失败:', error)
        return []
      }
    },

    // 获取歌词
    async lyric(musicInfo) {
      try {
        const response = await httpRequest(`${BASE_URL}/api/lyrics`, {
          body: JSON.stringify({
            id: musicInfo.songmid || musicInfo.id
          })
        })
        
        if (response.status === 200 && response.lyrics) {
          return {
            lyric: response.lyrics.lrc?.lyric || '',
            tlyric: response.lyrics.tlyric?.lyric || '',
            rlyric: null,
            lxlyric: null
          }
        } else {
          return {
            lyric: '',
            tlyric: '',
            rlyric: null,
            lxlyric: null
          }
        }
      } catch (error) {
        console.error('获取歌词失败:', error)
        return {
          lyric: '',
          tlyric: '',
          rlyric: null,
          lxlyric: null
        }
      }
    },

    // 获取封面图片
    async pic(musicInfo) {
      try {
        // 网易云音乐的封面通常在musicInfo中已包含
        if (musicInfo.img) {
          return musicInfo.img
        }
        
        // 如果没有封面信息，返回默认封面
        return ''
      } catch (error) {
        console.error('获取封面失败:', error)
        return ''
      }
    }
  }
}

// 注册API请求事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log(`[网易云音乐源] 收到请求: source=${source}, action=${action}`)
  
  switch (action) {
    case 'musicUrl':
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
        .catch(err => {
          console.error('[网易云音乐源] 获取音乐URL失败:', err)
          return Promise.reject(err)
        })
        
    case 'lyric':
      return apis[source].lyric(info.musicInfo)
        .catch(err => {
          console.error('[网易云音乐源] 获取歌词失败:', err)
          return Promise.reject(err)
        })
        
    case 'pic':
      return apis[source].pic(info.musicInfo)
        .catch(err => {
          console.error('[网易云音乐源] 获取封面失败:', err)
          return Promise.reject(err)
        })

    case 'search':
      return apis[source].search(info.keyword, info.page)
        .catch(err => {
          console.error('[网易云音乐源] 搜索失败:', err)
          return Promise.reject(err)
        })

    default:
      return Promise.reject(new Error(`不支持的操作: ${action}`))
  }
})

// 发送初始化完成事件
send(EVENT_NAMES.inited, {
  openDevTools: false, // 生产环境设为false，开发时可设为true
  sources: {
    wy: {
      name: '网易云音乐',
      type: 'music',
      actions: ['musicUrl', 'lyric', 'pic'], // 注意：search功能需要落雪音乐官方支持
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})

console.log('[网易云音乐源] 初始化完成')
